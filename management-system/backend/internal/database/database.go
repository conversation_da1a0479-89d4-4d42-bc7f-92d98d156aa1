package database

import (
	"fmt"
	"time"

	"instrument-management/internal/config"
	"instrument-management/internal/models"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var db *gorm.DB

// Init 初始化数据库连接
func Init() (*gorm.DB, error) {
	var err error

	// 构建数据库连接字符串
	dsn := buildDSN()

	// 配置GORM日志
	var gormLogger logger.Interface
	if config.GetString("app.env") == "production" {
		gormLogger = logger.Default.LogMode(logger.Silent)
	} else {
		gormLogger = logger.Default
	}

	// 连接数据库
	db, err = gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger,
		NowFunc: func() time.Time {
			return time.Now().Local()
		},
	})

	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取底层SQL DB失败: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxOpenConns(config.GetInt("database.max_open_conns"))
	sqlDB.SetMaxIdleConns(config.GetInt("database.max_idle_conns"))
	sqlDB.SetConnMaxLifetime(time.Duration(config.GetInt("database.conn_max_lifetime")) * time.Second)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("数据库连接测试失败: %w", err)
	}

	fmt.Println("数据库连接成功")

	// 自动迁移数据库表
	if err := autoMigrate(db); err != nil {
		return nil, fmt.Errorf("数据库迁移失败: %w", err)
	}

	return db, nil
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	return db
}

// Close 关闭数据库连接
func Close() error {
	if db != nil {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// buildDSN 构建数据库连接字符串
func buildDSN() string {
	host := config.GetString("database.host")
	port := config.GetInt("database.port")
	username := config.GetString("database.username")
	password := config.GetString("database.password")
	database := config.GetString("database.database")
	sslMode := config.GetString("database.ssl_mode")

	return fmt.Sprintf(
		"host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		host, port, username, password, database, sslMode,
	)
}

// autoMigrate 自动迁移数据库表
func autoMigrate(db *gorm.DB) error {
	// 导入所有模型
	models := []interface{}{
		&models.User{},
		&models.ResearchGroup{},
		&models.Instrument{},
		&models.WorkingHours{},
		&models.BillingConfig{},
		&models.UserTag{},
		&models.Reservation{},
		&models.SelfReservation{},
		&models.SampleSubmission{},
		&models.UsageRecord{},
		&models.TrainingRecord{},
		&models.Transaction{},
		&models.Bill{},
		&models.BillItem{},
		&models.DepositRecord{},
	}

	for _, model := range models {
		if err := db.AutoMigrate(model); err != nil {
			return fmt.Errorf("迁移表失败 %T: %w", model, err)
		}
	}

	fmt.Println("数据库迁移完成")
	return nil
}

// Transaction 执行数据库事务
func Transaction(fc func(tx *gorm.DB) error) error {
	return db.Transaction(fc)
}

// WithTimeout 设置查询超时
func WithTimeout(timeout time.Duration) *gorm.DB {
	return db.Session(&gorm.Session{Context: db.Statement.Context})
}


