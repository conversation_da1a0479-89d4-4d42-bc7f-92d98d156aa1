package services

import (
	"errors"
	"fmt"
	"time"

	"instrument-management/internal/models"

	"gorm.io/gorm"
)

// ReservationService 预约服务
type ReservationService struct {
	db *gorm.DB
}

// NewReservationService 创建预约服务实例
func NewReservationService(db *gorm.DB) *ReservationService {
	return &ReservationService{db: db}
}

// CreateSelfReservation 创建自主预约
func (s *ReservationService) CreateSelfReservation(req *CreateSelfReservationRequest, userID string) (*models.Reservation, error) {
	// 检查用户是否存在
	var user models.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		return nil, errors.New("用户不存在")
	}

	// 检查用户状态
	if user.Status != models.UserStatusActive {
		return nil, errors.New("用户未激活，无法预约")
	}

	// 检查仪器是否存在
	var instrument models.Instrument
	if err := s.db.Preload("ResponsibleUser").Where("id = ?", req.InstrumentID).First(&instrument).Error; err != nil {
		return nil, errors.New("仪器不存在")
	}

	// 检查仪器是否可预约
	if instrument.Status != models.InstrumentStatusOnline {
		return nil, errors.New("仪器当前不可预约")
	}

	if !instrument.RequiresReservation {
		return nil, errors.New("该仪器不需要预约")
	}

	// 检查是否需要培训
	if instrument.RequiresTraining {
		// 检查用户是否有培训记录
		var trainingCount int64
		if err := s.db.Model(&models.TrainingRecord{}).
			Where("user_id = ? AND instrument_id = ? AND status = ?",
				userID, req.InstrumentID, models.TrainingStatusCompleted).
			Count(&trainingCount).Error; err != nil {
			return nil, err
		}

		if trainingCount == 0 {
			return nil, errors.New("使用该仪器需要先完成培训")
		}
	}

	// 检查时间冲突
	if err := s.checkTimeConflict(req.InstrumentID, req.StartTime, req.EndTime); err != nil {
		return nil, err
	}

	// 检查预约时间是否在工作时间内
	if err := s.checkWorkingHours(req.InstrumentID, req.StartTime, req.EndTime); err != nil {
		return nil, err
	}

	// 创建预约
	reservation := &models.Reservation{
		InstrumentID: req.InstrumentID,
		UserID:       userID,
		Type:         models.ReservationTypeSelf,
		Status:       models.ReservationStatusPending,
		StartTime:    req.StartTime,
		EndTime:      req.EndTime,
		Notes:        req.Notes,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// 保存预约
	if err := s.db.Create(reservation).Error; err != nil {
		return nil, fmt.Errorf("创建预约失败: %w", err)
	}

	// 创建自主预约详情
	selfReservation := &models.SelfReservation{
		Reservation:     *reservation,
		TrainingRecordID: req.TrainingRecordID,
		FormData:        fmt.Sprintf("%v", req.FormData),
	}

	if err := s.db.Create(selfReservation).Error; err != nil {
		return nil, fmt.Errorf("创建预约详情失败: %w", err)
	}

	// 重新加载关联数据
	if err := s.db.Preload("Instrument").Preload("User").Where("id = ?", reservation.ID).First(reservation).Error; err != nil {
		return nil, err
	}

	return reservation, nil
}

// CreateSampleSubmission 创建送样预约
func (s *ReservationService) CreateSampleSubmission(req *CreateSampleSubmissionRequest, userID string) (*models.Reservation, error) {
	// 检查用户是否存在
	var user models.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		return nil, errors.New("用户不存在")
	}

	// 检查仪器是否存在
	var instrument models.Instrument
	if err := s.db.Where("id = ?", req.InstrumentID).First(&instrument).Error; err != nil {
		return nil, errors.New("仪器不存在")
	}

	// 检查仪器是否接受送样
	if !instrument.AcceptsSampleSubmission {
		return nil, errors.New("该仪器不接受送样预约")
	}

	// 创建预约
	reservation := &models.Reservation{
		InstrumentID: req.InstrumentID,
		UserID:       userID,
		Type:         models.ReservationTypeSample,
		Status:       models.ReservationStatusPending,
		StartTime:    req.StartTime,
		EndTime:      req.EndTime,
		Notes:        req.Notes,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// 保存预约
	if err := s.db.Create(reservation).Error; err != nil {
		return nil, fmt.Errorf("创建预约失败: %w", err)
	}

	// 创建送样预约详情
	sampleSubmission := &models.SampleSubmission{
		Reservation:       *reservation,
		SampleName:        req.SampleName,
		SampleDescription: req.SampleDescription,
		SampleCount:       req.SampleCount,
		Attachments:       fmt.Sprintf("%v", req.Attachments),
		SampleStatus:      models.SampleStatusPending,
	}

	if err := s.db.Create(sampleSubmission).Error; err != nil {
		return nil, fmt.Errorf("创建送样详情失败: %w", err)
	}

	// 重新加载关联数据
	if err := s.db.Preload("Instrument").Preload("User").Where("id = ?", reservation.ID).First(reservation).Error; err != nil {
		return nil, err
	}

	return reservation, nil
}

// GetReservation 获取预约详情
func (s *ReservationService) GetReservation(reservationID string, userID string) (*models.Reservation, error) {
	var reservation models.Reservation
	if err := s.db.Preload("Instrument").Preload("User").Where("id = ?", reservationID).First(&reservation).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("预约不存在")
		}
		return nil, err
	}

	// 检查权限：只能查看自己的预约或负责的仪器的预约
	var user models.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		return nil, errors.New("用户不存在")
	}

	if reservation.UserID != userID && reservation.Instrument.ResponsibleUserID != userID && user.Role != models.UserRoleAdmin {
		return nil, errors.New("无权限查看此预约")
	}

	return &reservation, nil
}

// ListReservations 获取预约列表
func (s *ReservationService) ListReservations(filter *ReservationFilter, userID string, page, pageSize int) ([]*models.Reservation, int64, error) {
	var reservations []*models.Reservation
	var total int64

	query := s.db.Model(&models.Reservation{}).Preload("Instrument").Preload("User")

	// 检查用户角色
	var user models.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		return nil, 0, errors.New("用户不存在")
	}

	// 根据角色设置查询权限
	if user.Role == models.UserRoleAdmin {
		// 管理员可以看到所有预约
	} else if user.Role == models.UserRoleTechnician {
		// 仪器负责人可以看到负责仪器的预约
		query = query.Where("instrument_id IN (SELECT id FROM instruments WHERE responsible_user_id = ?)", userID)
	} else {
		// 普通用户只能看到自己的预约
		query = query.Where("user_id = ?", userID)
	}

	// 应用过滤条件
	if filter != nil {
		if filter.Status != "" {
			query = query.Where("status = ?", filter.Status)
		}
		if filter.Type != "" {
			query = query.Where("type = ?", filter.Type)
		}
		if filter.InstrumentID != "" {
			query = query.Where("instrument_id = ?", filter.InstrumentID)
		}
		if filter.StartDate != nil {
			query = query.Where("start_time >= ?", filter.StartDate)
		}
		if filter.EndDate != nil {
			query = query.Where("end_time <= ?", filter.EndDate)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&reservations).Error; err != nil {
		return nil, 0, err
	}

	return reservations, total, nil
}

// UpdateReservation 更新预约
func (s *ReservationService) UpdateReservation(reservationID string, req *UpdateReservationRequest, operatorID string) (*models.Reservation, error) {
	var reservation models.Reservation
	if err := s.db.Preload("Instrument").Where("id = ?", reservationID).First(&reservation).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("预约不存在")
		}
		return nil, err
	}

	// 检查权限
	var operator models.User
	if err := s.db.Where("id = ?", operatorID).First(&operator).Error; err != nil {
		return nil, errors.New("操作者不存在")
	}

	canUpdate := false
	if operator.Role == models.UserRoleAdmin {
		canUpdate = true
	} else if operator.Role == models.UserRoleTechnician && reservation.Instrument.ResponsibleUserID == operatorID {
		canUpdate = true
	} else if reservation.UserID == operatorID && reservation.Status == models.ReservationStatusPending {
		canUpdate = true
	}

	if !canUpdate {
		return nil, errors.New("无权限修改此预约")
	}

	// 检查是否可以修改
	if reservation.Status == models.ReservationStatusCompleted || reservation.Status == models.ReservationStatusCancelled {
		return nil, errors.New("已完成的预约不能修改")
	}

	// 更新字段
	if req.StartTime != nil {
		reservation.StartTime = *req.StartTime
	}
	if req.EndTime != nil {
		reservation.EndTime = *req.EndTime
	}
	if req.Notes != nil {
		reservation.Notes = req.Notes
	}

	reservation.UpdatedAt = time.Now()

	// 检查时间冲突（如果时间有变更）
	if req.StartTime != nil || req.EndTime != nil {
		if err := s.checkTimeConflict(reservation.InstrumentID, reservation.StartTime, reservation.EndTime); err != nil {
			return nil, err
		}
	}

	if err := s.db.Save(&reservation).Error; err != nil {
		return nil, fmt.Errorf("更新预约失败: %w", err)
	}

	// 重新加载关联数据
	if err := s.db.Preload("Instrument").Preload("User").Where("id = ?", reservation.ID).First(&reservation).Error; err != nil {
		return nil, err
	}

	return &reservation, nil
}

// ApproveReservation 批准预约
func (s *ReservationService) ApproveReservation(reservationID string, operatorID string) error {
	var reservation models.Reservation
	if err := s.db.Preload("Instrument").Where("id = ?", reservationID).First(&reservation).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("预约不存在")
		}
		return err
	}

	// 检查权限
	var operator models.User
	if err := s.db.Where("id = ?", operatorID).First(&operator).Error; err != nil {
		return errors.New("操作者不存在")
	}

	if operator.Role != models.UserRoleAdmin && reservation.Instrument.ResponsibleUserID != operatorID {
		return errors.New("无权限批准此预约")
	}

	// 检查预约状态
	if reservation.Status != models.ReservationStatusPending {
		return errors.New("只能批准待审批的预约")
	}

	// 更新预约状态
	now := time.Now()
	reservation.Status = models.ReservationStatusApproved
	reservation.ApprovedAt = &now
	reservation.ApprovedBy = &operatorID
	reservation.UpdatedAt = now

	if err := s.db.Save(&reservation).Error; err != nil {
		return fmt.Errorf("批准预约失败: %w", err)
	}

	return nil
}

// RejectReservation 拒绝预约
func (s *ReservationService) RejectReservation(reservationID string, reason string, operatorID string) error {
	var reservation models.Reservation
	if err := s.db.Preload("Instrument").Where("id = ?", reservationID).First(&reservation).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("预约不存在")
		}
		return err
	}

	// 检查权限
	var operator models.User
	if err := s.db.Where("id = ?", operatorID).First(&operator).Error; err != nil {
		return errors.New("操作者不存在")
	}

	if operator.Role != models.UserRoleAdmin && reservation.Instrument.ResponsibleUserID != operatorID {
		return errors.New("无权限拒绝此预约")
	}

	// 检查预约状态
	if reservation.Status != models.ReservationStatusPending {
		return errors.New("只能拒绝待审批的预约")
	}

	// 更新预约状态
	now := time.Now()
	reservation.Status = models.ReservationStatusRejected
	reservation.RejectedAt = &now
	reservation.RejectedBy = &operatorID
	reservation.RejectReason = &reason
	reservation.UpdatedAt = now

	if err := s.db.Save(&reservation).Error; err != nil {
		return fmt.Errorf("拒绝预约失败: %w", err)
	}

	return nil
}

// CancelReservation 取消预约
func (s *ReservationService) CancelReservation(reservationID string, operatorID string) error {
	var reservation models.Reservation
	if err := s.db.Where("id = ?", reservationID).First(&reservation).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("预约不存在")
		}
		return err
	}

	// 检查权限
	var operator models.User
	if err := s.db.Where("id = ?", operatorID).First(&operator).Error; err != nil {
		return errors.New("操作者不存在")
	}

	canCancel := false
	if operator.Role == models.UserRoleAdmin {
		canCancel = true
	} else if operator.Role == models.UserRoleTechnician && reservation.Instrument.ResponsibleUserID == operatorID {
		canCancel = true
	} else if reservation.UserID == operatorID {
		canCancel = true
	}

	if !canCancel {
		return errors.New("无权限取消此预约")
	}

	// 检查预约状态
	if reservation.Status == models.ReservationStatusCompleted || reservation.Status == models.ReservationStatusCancelled {
		return errors.New("已完成的预约不能取消")
	}

	// 更新预约状态
	reservation.Status = models.ReservationStatusCancelled
	reservation.UpdatedAt = time.Now()

	if err := s.db.Save(&reservation).Error; err != nil {
		return fmt.Errorf("取消预约失败: %w", err)
	}

	return nil
}

// checkTimeConflict 检查时间冲突
func (s *ReservationService) checkTimeConflict(instrumentID string, startTime, endTime time.Time) error {
	var count int64
	err := s.db.Model(&models.Reservation{}).
		Where("instrument_id = ? AND status IN ? AND ((start_time <= ? AND end_time > ?) OR (start_time < ? AND end_time >= ?))",
			instrumentID,
			[]models.ReservationStatus{models.ReservationStatusApproved, models.ReservationStatusInProgress},
			startTime, startTime, endTime, endTime).
		Count(&count).Error

	if err != nil {
		return err
	}

	if count > 0 {
		return errors.New("预约时间与现有预约冲突")
	}

	return nil
}

// checkWorkingHours 检查工作时间
func (s *ReservationService) checkWorkingHours(instrumentID string, startTime, endTime time.Time) error {
	var workingHours []models.WorkingHours
	if err := s.db.Where("instrument_id = ?", instrumentID).Find(&workingHours).Error; err != nil {
		return err
	}

	// 如果没有设置工作时间，默认允许
	if len(workingHours) == 0 {
		return nil
	}

	// 检查预约时间是否在工作时间内
	for _, wh := range workingHours {
		if !wh.IsWorkingDay {
			continue
		}

		// 检查是否是工作日
		if int(startTime.Weekday()) == wh.DayOfWeek || int(endTime.Weekday()) == wh.DayOfWeek {
			// 简单的时间检查（可以根据需要完善）
			if startTime.Format("15:04") >= wh.StartTime && endTime.Format("15:04") <= wh.EndTime {
				return nil
			}
		}
	}

	return errors.New("预约时间不在工作时间内")
}

// ReservationFilter 预约过滤器
type ReservationFilter struct {
	Status       string     `json:"status,omitempty"`
	Type         string     `json:"type,omitempty"`
	InstrumentID string     `json:"instrument_id,omitempty"`
	StartDate    *time.Time `json:"start_date,omitempty"`
	EndDate      *time.Time `json:"end_date,omitempty"`
}

// CreateSelfReservationRequest 创建自主预约请求
type CreateSelfReservationRequest struct {
	InstrumentID     string    `json:"instrument_id" binding:"required"`
	StartTime        time.Time `json:"start_time" binding:"required"`
	EndTime          time.Time `json:"end_time" binding:"required"`
	Notes            *string   `json:"notes,omitempty"`
	TrainingRecordID *string   `json:"training_record_id,omitempty"`
	FormData         []string  `json:"form_data,omitempty"`
}

// CreateSampleSubmissionRequest 创建送样预约请求
type CreateSampleSubmissionRequest struct {
	InstrumentID     string    `json:"instrument_id" binding:"required"`
	StartTime        time.Time `json:"start_time" binding:"required"`
	EndTime          time.Time `json:"end_time" binding:"required"`
	Notes            *string   `json:"notes,omitempty"`
	SampleName       string    `json:"sample_name" binding:"required"`
	SampleDescription *string  `json:"sample_description,omitempty"`
	SampleCount      int       `json:"sample_count"`
	Attachments      []string  `json:"attachments,omitempty"`
}

// UpdateReservationRequest 更新预约请求
type UpdateReservationRequest struct {
	StartTime *time.Time `json:"start_time,omitempty"`
	EndTime   *time.Time `json:"end_time,omitempty"`
	Notes     *string    `json:"notes,omitempty"`
}


