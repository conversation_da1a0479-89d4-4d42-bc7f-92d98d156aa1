package services

import (
	"errors"
	"fmt"
	"time"

	"instrument-management/internal/models"

	"gorm.io/gorm"
)

// BillingService 计费服务
type BillingService struct {
	db *gorm.DB
}

// NewBillingService 创建计费服务实例
func NewBillingService(db *gorm.DB) *BillingService {
	return &BillingService{db: db}
}

// CalculateReservationCharge 计算预约费用
func (s *BillingService) CalculateReservationCharge(reservationID string) (*ChargeCalculation, error) {
	// 获取预约信息
	var reservation models.Reservation
	if err := s.db.Preload("Instrument").Preload("User").Where("id = ?", reservationID).First(&reservation).Error; err != nil {
		return nil, fmt.Errorf("获取预约信息失败: %w", err)
	}

	// 获取仪器计费配置
	var billingConfig models.BillingConfig
	if err := s.db.Where("instrument_id = ?", reservation.InstrumentID).First(&billingConfig).Error; err != nil {
		return nil, fmt.Errorf("获取计费配置失败: %w", err)
	}

	// 计算使用时长（小时）
	duration := reservation.EndTime.Sub(reservation.StartTime).Hours()

	// 基础费用计算
	var baseAmount float64
	switch billingConfig.BillingType {
	case models.BillingTypePerHour:
		baseAmount = duration * billingConfig.BaseRate
	case models.BillingTypePerSample:
		// 送样预约按样品计费，自主预约按时长
		if reservation.Type == models.ReservationTypeSample {
			// 从送样预约表获取样品数量
			var sampleSubmission models.SampleSubmission
			if err := s.db.Where("id = ?", reservation.ID).First(&sampleSubmission).Error; err != nil {
				return nil, fmt.Errorf("获取送样信息失败: %w", err)
			}
			baseAmount = float64(sampleSubmission.SampleCount) * billingConfig.BaseRate
		} else {
			baseAmount = duration * billingConfig.BaseRate
		}
	case models.BillingTypeFlatRate:
		baseAmount = billingConfig.BaseRate
	default:
		return nil, errors.New("不支持的计费类型")
	}

	// 计算用户标签折扣
	discount := s.calculateUserDiscount(reservation.UserID, billingConfig)

	// 最终金额
	finalAmount := baseAmount * (1 - discount)

	return &ChargeCalculation{
		ReservationID: reservationID,
		BaseAmount:    baseAmount,
		Discount:      discount,
		FinalAmount:   finalAmount,
		BillingType:   billingConfig.BillingType,
		Duration:      duration,
	}, nil
}

// CreateTransaction 创建交易记录
func (s *BillingService) CreateTransaction(req *CreateTransactionRequest, operatorID string) (*models.Transaction, error) {
	// 检查操作者权限
	var operator models.User
	if err := s.db.Where("id = ?", operatorID).First(&operator).Error; err != nil {
		return nil, errors.New("操作者不存在")
	}

	if operator.Role != models.UserRoleAdmin && operator.Role != models.UserRoleTechnician {
		return nil, errors.New("无权限创建交易记录")
	}

	// 检查用户和课题组
	var user models.User
	if err := s.db.Preload("ResearchGroup").Where("id = ?", req.UserID).First(&user).Error; err != nil {
		return nil, errors.New("用户不存在")
	}

	if user.ResearchGroupID == nil {
		return nil, errors.New("用户未分配到课题组")
	}

	// 创建交易记录
	transaction := &models.Transaction{
		UserID:            req.UserID,
		ResearchGroupID:   *user.ResearchGroupID,
		Type:              models.TransactionType(req.Type),
		Amount:            req.Amount,
		Description:       req.Description,
		RelatedReservationID: req.RelatedReservationID,
		RelatedInstrumentID:  req.RelatedInstrumentID,
		Status:            models.PaymentStatusCompleted,
		TransactionDate:   time.Now(),
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	if err := s.db.Create(transaction).Error; err != nil {
		return nil, fmt.Errorf("创建交易记录失败: %w", err)
	}

	// 更新课题组余额
	if err := s.updateResearchGroupBalance(*user.ResearchGroupID, models.TransactionType(req.Type), req.Amount); err != nil {
		return nil, fmt.Errorf("更新课题组余额失败: %w", err)
	}

	return transaction, nil
}

// CreateBill 创建账单
func (s *BillingService) CreateBill(researchGroupID string, title, description string, operatorID string) (*models.Bill, error) {
	// 检查操作者权限
	var operator models.User
	if err := s.db.Where("id = ?", operatorID).First(&operator).Error; err != nil {
		return nil, errors.New("操作者不存在")
	}

	if operator.Role != models.UserRoleAdmin {
		return nil, errors.New("只有管理员可以创建账单")
	}

	// 获取课题组未结算的交易
	var transactions []models.Transaction
	if err := s.db.Where("research_group_id = ? AND status = ? AND created_at >= ?",
		researchGroupID, models.PaymentStatusCompleted, time.Now().AddDate(0, -1, 0)).
		Find(&transactions).Error; err != nil {
		return nil, err
	}

	if len(transactions) == 0 {
		return nil, errors.New("没有需要结算的交易")
	}

	// 计算账单总金额
	totalAmount := 0.0
	var billItems []models.BillItem

	for _, transaction := range transactions {
		totalAmount += transaction.Amount

		billItem := models.BillItem{
			ReservationID: getStringValue(transaction.RelatedReservationID),
			UsageDate:     transaction.TransactionDate,
			Amount:        transaction.Amount,
			Notes:         &transaction.Description,
		}

		// 如果有关联预约，获取更多信息
		if transaction.RelatedReservationID != nil {
			var reservation models.Reservation
			if err := s.db.Preload("Instrument").Where("id = ?", *transaction.RelatedReservationID).First(&reservation).Error; err == nil {
				billItem.InstrumentName = reservation.Instrument.Name
				billItem.Duration = reservation.EndTime.Sub(reservation.StartTime).Hours()
				billItem.UnitPrice = transaction.Amount / billItem.Duration
				billItem.Quantity = billItem.Duration
			}
		}

		billItems = append(billItems, billItem)
	}

	// 创建账单
	bill := &models.Bill{
		ResearchGroupID: researchGroupID,
		Title:           title,
		Description:     description,
		TotalAmount:     totalAmount,
		Status:          models.BillStatusUnpaid,
		BillDate:        time.Now(),
		DueDate:         func() *time.Time { t := time.Now().AddDate(0, 0, 30); return &t }(), // 30天后到期
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	if err := s.db.Create(bill).Error; err != nil {
		return nil, fmt.Errorf("创建账单失败: %w", err)
	}

	// 创建账单项目
	for i := range billItems {
		billItems[i].BillID = bill.ID
		if err := s.db.Create(&billItems[i]).Error; err != nil {
			return nil, fmt.Errorf("创建账单项目失败: %w", err)
		}
	}

	return bill, nil
}

// PayBill 支付账单
func (s *BillingService) PayBill(billID string, operatorID string) error {
	// 获取账单信息
	var bill models.Bill
	if err := s.db.Preload("ResearchGroup").Where("id = ?", billID).First(&bill).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("账单不存在")
		}
		return err
	}

	// 检查账单状态
	if bill.Status != models.BillStatusUnpaid {
		return errors.New("账单已支付或已取消")
	}

	// 检查操作者权限
	var operator models.User
	if err := s.db.Where("id = ?", operatorID).First(&operator).Error; err != nil {
		return errors.New("操作者不存在")
	}

	if operator.Role != models.UserRoleAdmin && bill.ResearchGroup.LeaderID != operatorID {
		return errors.New("无权限支付此账单")
	}

	// 检查课题组余额
	if bill.ResearchGroup.Balance < bill.TotalAmount {
		return errors.New("课题组余额不足")
	}

	// 更新账单状态
	now := time.Now()
	bill.Status = models.BillStatusPaid
	bill.PaidAt = &now
	bill.UpdatedAt = now

	if err := s.db.Save(&bill).Error; err != nil {
		return fmt.Errorf("更新账单状态失败: %w", err)
	}

	// 扣除课题组余额
	bill.ResearchGroup.Balance -= bill.TotalAmount
	if err := s.db.Save(&bill.ResearchGroup).Error; err != nil {
		return fmt.Errorf("更新课题组余额失败: %w", err)
	}

	return nil
}

// GetFinancialStats 获取财务统计
func (s *BillingService) GetFinancialStats(researchGroupID *string) (*models.FinancialStats, error) {
	query := s.db.Model(&models.Transaction{})

	if researchGroupID != nil {
		query = query.Where("research_group_id = ?", *researchGroupID)
	}

	// 计算总收入
	var totalRevenue float64
	if err := query.Where("type = ? AND status = ?", models.TransactionTypeCharge, models.PaymentStatusCompleted).
		Select("COALESCE(SUM(amount), 0)").Scan(&totalRevenue).Error; err != nil {
		return nil, err
	}

	// 计算月收入
	var monthlyRevenue float64
	if err := query.Where("type = ? AND status = ? AND transaction_date >= ?",
		models.TransactionTypeCharge, models.PaymentStatusCompleted, time.Now().AddDate(0, -1, 1)).
		Select("COALESCE(SUM(amount), 0)").Scan(&monthlyRevenue).Error; err != nil {
		return nil, err
	}

	// 计算待支付金额
	var pendingPayments float64
	if err := s.db.Model(&models.Bill{}).
		Where("status = ?", models.BillStatusUnpaid).
		Select("COALESCE(SUM(total_amount), 0)").Scan(&pendingPayments).Error; err != nil {
		return nil, err
	}

	// 获取总交易数
	var totalTransactions int64
	if err := query.Where("status = ?", models.PaymentStatusCompleted).Count(&totalTransactions).Error; err != nil {
		return nil, err
	}

	// 按仪器统计收入
	instrumentStats := make(map[string]float64)
	rows, err := query.Where("type = ? AND status = ? AND related_instrument_id IS NOT NULL",
		models.TransactionTypeCharge, models.PaymentStatusCompleted).
		Select("related_instrument_id, SUM(amount) as total").
		Group("related_instrument_id").Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var instrumentID string
		var total float64
		rows.Scan(&instrumentID, &total)
		instrumentStats[instrumentID] = total
	}

	// 按用户类型统计收入
	userTypeStats := make(map[string]float64)
	// 这里需要关联用户角色查询，暂时简化处理

	// 获取月度收入趋势
	var monthlyTrend []models.MonthlyRevenue
	if err := query.Where("type = ? AND status = ?", models.TransactionTypeCharge, models.PaymentStatusCompleted).
		Select("DATE_TRUNC('month', transaction_date) as month, COUNT(*) as transaction_count, SUM(amount) as revenue").
		Group("DATE_TRUNC('month', transaction_date)").
		Order("month DESC").
		Limit(12).
		Scan(&monthlyTrend).Error; err != nil {
		return nil, err
	}

	return &models.FinancialStats{
		TotalRevenue:        totalRevenue,
		MonthlyRevenue:      monthlyRevenue,
		PendingPayments:     pendingPayments,
		TotalTransactions:   int(totalTransactions),
		RevenueByInstrument: instrumentStats,
		RevenueByUserType:   userTypeStats,
		MonthlyTrend:        monthlyTrend,
	}, nil
}

// calculateUserDiscount 计算用户折扣
func (s *BillingService) calculateUserDiscount(userID string, billingConfig models.BillingConfig) float64 {
	// 获取用户标签
	var userTags []models.UserTag
	if err := s.db.Where("instrument_id = ?", billingConfig.InstrumentID).Find(&userTags).Error; err != nil {
		return 0
	}

	// 检查用户是否属于某个标签（这里简化处理，实际应该检查用户的具体属性）
	// 暂时返回最大折扣作为示例
	maxDiscount := 0.0
	for _, tag := range userTags {
		if tag.Discount > maxDiscount {
			maxDiscount = tag.Discount
		}
	}

	return maxDiscount
}

// updateResearchGroupBalance 更新课题组余额
func (s *BillingService) updateResearchGroupBalance(researchGroupID string, transactionType models.TransactionType, amount float64) error {
	var researchGroup models.ResearchGroup
	if err := s.db.Where("id = ?", researchGroupID).First(&researchGroup).Error; err != nil {
		return err
	}

	switch transactionType {
	case models.TransactionTypeCharge:
		researchGroup.Balance -= amount
	case models.TransactionTypeRefund:
		researchGroup.Balance += amount
	case models.TransactionTypeDeposit:
		researchGroup.Balance += amount
	}

	return s.db.Save(&researchGroup).Error
}

// ListTransactions 获取交易记录列表
func (s *BillingService) ListTransactions(filter *TransactionFilter, page, pageSize int) ([]*models.Transaction, int64, error) {
	var transactions []*models.Transaction
	var total int64

	query := s.db.Model(&models.Transaction{}).Preload("User").Preload("ResearchGroup").Preload("Reservation").Preload("Instrument")

	// 应用过滤条件
	if filter != nil {
		if filter.ResearchGroupID != "" {
			query = query.Where("research_group_id = ?", filter.ResearchGroupID)
		}
		if filter.Type != "" {
			query = query.Where("type = ?", filter.Type)
		}
		if filter.Status != "" {
			query = query.Where("status = ?", filter.Status)
		}
		if filter.StartDate != nil {
			query = query.Where("transaction_date >= ?", filter.StartDate)
		}
		if filter.EndDate != nil {
			query = query.Where("transaction_date <= ?", filter.EndDate)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&transactions).Error; err != nil {
		return nil, 0, err
	}

	return transactions, total, nil
}

// ListBills 获取账单列表
func (s *BillingService) ListBills(filter *BillFilter, page, pageSize int) ([]*models.Bill, int64, error) {
	var bills []*models.Bill
	var total int64

	query := s.db.Model(&models.Bill{}).Preload("ResearchGroup")

	// 应用过滤条件
	if filter != nil {
		if filter.ResearchGroupID != "" {
			query = query.Where("research_group_id = ?", filter.ResearchGroupID)
		}
		if filter.Status != "" {
			query = query.Where("status = ?", filter.Status)
		}
		if filter.StartDate != nil {
			query = query.Where("bill_date >= ?", filter.StartDate)
		}
		if filter.EndDate != nil {
			query = query.Where("bill_date <= ?", filter.EndDate)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&bills).Error; err != nil {
		return nil, 0, err
	}

	return bills, total, nil
}

// CreateDeposit 创建充值记录
func (s *BillingService) CreateDeposit(req *CreateDepositRequest, operatorID string) (*models.DepositRecord, error) {
	// 检查操作者权限
	var operator models.User
	if err := s.db.Where("id = ?", operatorID).First(&operator).Error; err != nil {
		return nil, errors.New("操作者不存在")
	}

	if operator.Role != models.UserRoleAdmin {
		return nil, errors.New("只有管理员可以创建充值记录")
	}

	// 检查课题组
	var researchGroup models.ResearchGroup
	if err := s.db.Where("id = ?", req.ResearchGroupID).First(&researchGroup).Error; err != nil {
		return nil, errors.New("课题组不存在")
	}

	// 创建充值记录
	deposit := &models.DepositRecord{
		ResearchGroupID: req.ResearchGroupID,
		OperatorID:      operatorID,
		Amount:          req.Amount,
		PaymentMethod:   req.PaymentMethod,
		TransactionID:   req.TransactionID,
		Status:          models.DepositStatusCompleted,
		Notes:           req.Notes,
		DepositDate:     time.Now(),
		ProcessedAt:     func() *time.Time { t := time.Now(); return &t }(),
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	if err := s.db.Create(deposit).Error; err != nil {
		return nil, fmt.Errorf("创建充值记录失败: %w", err)
	}

	// 更新课题组余额
	researchGroup.Balance += req.Amount
	if err := s.db.Save(&researchGroup).Error; err != nil {
		return nil, fmt.Errorf("更新课题组余额失败: %w", err)
	}

	// 创建交易记录
	transaction := &models.Transaction{
		UserID:          researchGroup.LeaderID,
		ResearchGroupID: req.ResearchGroupID,
		Type:            models.TransactionTypeDeposit,
		Amount:          req.Amount,
		Description:     fmt.Sprintf("充值：%s", req.PaymentMethod),
		Status:          models.PaymentStatusCompleted,
		TransactionDate: time.Now(),
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	if err := s.db.Create(transaction).Error; err != nil {
		return nil, fmt.Errorf("创建交易记录失败: %w", err)
	}

	return deposit, nil
}

// ChargeCalculation 费用计算结果
type ChargeCalculation struct {
	ReservationID string               `json:"reservation_id"`
	BaseAmount    float64              `json:"base_amount"`
	Discount      float64              `json:"discount"`
	FinalAmount   float64              `json:"final_amount"`
	BillingType   models.BillingType   `json:"billing_type"`
	Duration      float64              `json:"duration"`
}

// CreateTransactionRequest 创建交易请求
type CreateTransactionRequest struct {
	UserID               string  `json:"user_id" binding:"required"`
	Type                 string  `json:"type" binding:"required"`
	Amount               float64 `json:"amount" binding:"required"`
	Description          string  `json:"description" binding:"required"`
	RelatedReservationID *string `json:"related_reservation_id,omitempty"`
	RelatedInstrumentID  *string `json:"related_instrument_id,omitempty"`
}

// CreateDepositRequest 创建充值请求
type CreateDepositRequest struct {
	ResearchGroupID string  `json:"research_group_id" binding:"required"`
	Amount          float64 `json:"amount" binding:"required"`
	PaymentMethod   string  `json:"payment_method" binding:"required"`
	TransactionID   *string `json:"transaction_id,omitempty"`
	Notes           *string `json:"notes,omitempty"`
}

// TransactionFilter 交易过滤器
type TransactionFilter struct {
	ResearchGroupID string     `json:"research_group_id,omitempty"`
	Type            string     `json:"type,omitempty"`
	Status          string     `json:"status,omitempty"`
	StartDate       *time.Time `json:"start_date,omitempty"`
	EndDate         *time.Time `json:"end_date,omitempty"`
}

// BillFilter 账单过滤器
type BillFilter struct {
	ResearchGroupID string     `json:"research_group_id,omitempty"`
	Status          string     `json:"status,omitempty"`
	StartDate       *time.Time `json:"start_date,omitempty"`
	EndDate         *time.Time `json:"end_date,omitempty"`
}

// getStringValue 辅助函数：安全获取字符串指针的值
func getStringValue(ptr *string) string {
	if ptr != nil {
		return *ptr
	}
	return ""
}

