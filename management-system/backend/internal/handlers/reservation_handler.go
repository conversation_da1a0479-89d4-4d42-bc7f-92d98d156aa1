package handlers

import (
	"net/http"
	"strconv"
	"time"

	"instrument-management/internal/models"
	"instrument-management/internal/services"

	"github.com/gin-gonic/gin"
)

// ReservationHandler 预约处理器
type ReservationHandler struct {
	reservationService *services.ReservationService
}

// NewReservationHandler 创建预约处理器
func NewReservationHandler(reservationService *services.ReservationService) *ReservationHandler {
	return &ReservationHandler{
		reservationService: reservationService,
	}
}

// CreateSelfReservation 创建自主预约
// @Summary 创建自主预约
// @Description 创建自主预约，需要培训记录和时间检查
// @Tags 预约
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateSelfReservationRequest true "创建自主预约请求"
// @Success 201 {object} ReservationResponse
// @Failure 400 {object} ErrorResponse
// @Router /reservations/self [post]
func (h *ReservationHandler) CreateSelfReservation(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	var req CreateSelfReservationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	reservation, err := h.reservationService.CreateSelfReservation(&services.CreateSelfReservationRequest{
		InstrumentID:     req.InstrumentID,
		StartTime:        req.StartTime,
		EndTime:          req.EndTime,
		Notes:            req.Notes,
		TrainingRecordID: req.TrainingRecordID,
		FormData:         req.FormData,
	}, userID.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "创建预约失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, ReservationResponse{
		Success: true,
		Message: "创建成功",
		Data:    reservation,
	})
}

// CreateSampleSubmission 创建送样预约
// @Summary 创建送样预约
// @Description 创建送样预约，由仪器负责人处理测试
// @Tags 预约
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateSampleSubmissionRequest true "创建送样预约请求"
// @Success 201 {object} ReservationResponse
// @Failure 400 {object} ErrorResponse
// @Router /reservations/sample [post]
func (h *ReservationHandler) CreateSampleSubmission(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	var req CreateSampleSubmissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	reservation, err := h.reservationService.CreateSampleSubmission(&services.CreateSampleSubmissionRequest{
		InstrumentID:     req.InstrumentID,
		StartTime:        req.StartTime,
		EndTime:          req.EndTime,
		Notes:            req.Notes,
		SampleName:       req.SampleName,
		SampleDescription: req.SampleDescription,
		SampleCount:      req.SampleCount,
		Attachments:      req.Attachments,
	}, userID.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "创建预约失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, ReservationResponse{
		Success: true,
		Message: "创建成功",
		Data:    reservation,
	})
}

// GetReservation 获取预约详情
// @Summary 获取预约详情
// @Description 获取指定预约的详细信息
// @Tags 预约
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "预约ID"
// @Success 200 {object} ReservationResponse
// @Failure 404 {object} ErrorResponse
// @Router /reservations/{id} [get]
func (h *ReservationHandler) GetReservation(c *gin.Context) {
	reservationID := c.Param("id")
	if reservationID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "预约ID不能为空",
		})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	reservation, err := h.reservationService.GetReservation(reservationID, userID.(string))
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Success: false,
			Message: "获取预约失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, ReservationResponse{
		Success: true,
		Message: "获取成功",
		Data:    reservation,
	})
}

// ListReservations 获取预约列表
// @Summary 获取预约列表
// @Description 获取预约列表，支持过滤和分页
// @Tags 预约
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param status query string false "预约状态"
// @Param type query string false "预约类型"
// @Param instrument_id query string false "仪器ID"
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} ReservationListResponse
// @Router /reservations [get]
func (h *ReservationHandler) ListReservations(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 构建过滤器
	filter := &services.ReservationFilter{
		Status:       c.Query("status"),
		Type:         c.Query("type"),
		InstrumentID: c.Query("instrument_id"),
	}

	// 解析日期参数
	if startDateStr := c.Query("start_date"); startDateStr != "" {
		if startDate, err := time.Parse("2006-01-02", startDateStr); err == nil {
			filter.StartDate = &startDate
		}
	}
	if endDateStr := c.Query("end_date"); endDateStr != "" {
		if endDate, err := time.Parse("2006-01-02", endDateStr); err == nil {
			filter.EndDate = &endDate
		}
	}

	// 分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	reservations, total, err := h.reservationService.ListReservations(filter, userID.(string), page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Message: "获取预约列表失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, ReservationListResponse{
		Success: true,
		Message: "获取成功",
		Data: ReservationListData{
			Reservations: reservations,
			Pagination: Pagination{
				Page:     page,
				PageSize: pageSize,
				Total:    int(total),
			},
		},
	})
}

// UpdateReservation 更新预约
// @Summary 更新预约
// @Description 更新预约信息（时间、备注等）
// @Tags 预约
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "预约ID"
// @Param request body UpdateReservationRequest true "更新预约请求"
// @Success 200 {object} ReservationResponse
// @Failure 403 {object} ErrorResponse
// @Router /reservations/{id} [put]
func (h *ReservationHandler) UpdateReservation(c *gin.Context) {
	reservationID := c.Param("id")
	if reservationID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "预约ID不能为空",
		})
		return
	}

	operatorID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	var req UpdateReservationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	reservation, err := h.reservationService.UpdateReservation(reservationID, &services.UpdateReservationRequest{
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Notes:     req.Notes,
	}, operatorID.(string))
	if err != nil {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Success: false,
			Message: "更新预约失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, ReservationResponse{
		Success: true,
		Message: "更新成功",
		Data:    reservation,
	})
}

// ApproveReservation 批准预约
// @Summary 批准预约
// @Description 仪器负责人批准预约申请
// @Tags 预约
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "预约ID"
// @Success 200 {object} SuccessResponse
// @Failure 403 {object} ErrorResponse
// @Router /reservations/{id}/approve [post]
func (h *ReservationHandler) ApproveReservation(c *gin.Context) {
	reservationID := c.Param("id")
	if reservationID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "预约ID不能为空",
		})
		return
	}

	operatorID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	err := h.reservationService.ApproveReservation(reservationID, operatorID.(string))
	if err != nil {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Success: false,
			Message: "批准预约失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "批准成功",
	})
}

// RejectReservation 拒绝预约
// @Summary 拒绝预约
// @Description 仪器负责人拒绝预约申请
// @Tags 预约
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "预约ID"
// @Param request body RejectReservationRequest true "拒绝预约请求"
// @Success 200 {object} SuccessResponse
// @Failure 403 {object} ErrorResponse
// @Router /reservations/{id}/reject [post]
func (h *ReservationHandler) RejectReservation(c *gin.Context) {
	reservationID := c.Param("id")
	if reservationID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "预约ID不能为空",
		})
		return
	}

	operatorID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	var req RejectReservationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	err := h.reservationService.RejectReservation(reservationID, req.Reason, operatorID.(string))
	if err != nil {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Success: false,
			Message: "拒绝预约失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "拒绝成功",
	})
}

// CancelReservation 取消预约
// @Summary 取消预约
// @Description 取消预约（用户或管理员操作）
// @Tags 预约
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "预约ID"
// @Success 200 {object} SuccessResponse
// @Failure 403 {object} ErrorResponse
// @Router /reservations/{id} [delete]
func (h *ReservationHandler) CancelReservation(c *gin.Context) {
	reservationID := c.Param("id")
	if reservationID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "预约ID不能为空",
		})
		return
	}

	operatorID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	err := h.reservationService.CancelReservation(reservationID, operatorID.(string))
	if err != nil {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Success: false,
			Message: "取消预约失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "取消成功",
	})
}

// CreateSelfReservationRequest 创建自主预约请求
type CreateSelfReservationRequest struct {
	InstrumentID     string    `json:"instrument_id" binding:"required"`
	StartTime        time.Time `json:"start_time" binding:"required"`
	EndTime          time.Time `json:"end_time" binding:"required"`
	Notes            *string   `json:"notes,omitempty"`
	TrainingRecordID *string   `json:"training_record_id,omitempty"`
	FormData         []string  `json:"form_data,omitempty"`
}

// CreateSampleSubmissionRequest 创建送样预约请求
type CreateSampleSubmissionRequest struct {
	InstrumentID     string    `json:"instrument_id" binding:"required"`
	StartTime        time.Time `json:"start_time" binding:"required"`
	EndTime          time.Time `json:"end_time" binding:"required"`
	Notes            *string   `json:"notes,omitempty"`
	SampleName       string    `json:"sample_name" binding:"required"`
	SampleDescription *string  `json:"sample_description,omitempty"`
	SampleCount      int       `json:"sample_count"`
	Attachments      []string  `json:"attachments,omitempty"`
}

// UpdateReservationRequest 更新预约请求
type UpdateReservationRequest struct {
	StartTime *time.Time `json:"start_time,omitempty"`
	EndTime   *time.Time `json:"end_time,omitempty"`
	Notes     *string    `json:"notes,omitempty"`
}

// RejectReservationRequest 拒绝预约请求
type RejectReservationRequest struct {
	Reason string `json:"reason" binding:"required"`
}

// ReservationResponse 预约响应
type ReservationResponse struct {
	Success bool                 `json:"success"`
	Message string               `json:"message"`
	Data    *models.Reservation  `json:"data"`
}

// ReservationListResponse 预约列表响应
type ReservationListResponse struct {
	Success bool                    `json:"success"`
	Message string                  `json:"message"`
	Data    ReservationListData     `json:"data"`
}

// ReservationListData 预约列表数据
type ReservationListData struct {
	Reservations []*models.Reservation `json:"reservations"`
	Pagination   Pagination            `json:"pagination"`
}
