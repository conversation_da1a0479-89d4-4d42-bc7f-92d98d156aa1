package handlers

import (
	"net/http"
	"strconv"

	"instrument-management/internal/models"
	"instrument-management/internal/services"

	"github.com/gin-gonic/gin"
)

// UserHandler 用户处理器
type UserHandler struct {
	userService *services.UserService
}

// NewUserHandler 创建用户处理器
func NewUserHandler(userService *services.UserService) *UserHandler {
	return &UserHandler{
		userService: userService,
	}
}

// GetProfile 获取用户个人资料
// @Summary 获取个人资料
// @Description 获取当前登录用户的个人资料
// @Tags 用户
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} UserResponse
// @Failure 401 {object} ErrorResponse
// @Router /users/profile [get]
func (h *UserHandler) GetProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	user, err := h.userService.GetUserByID(userID.(string))
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Success: false,
			Message: "获取用户资料失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, UserResponse{
		Success: true,
		Message: "获取成功",
		Data:    user,
	})
}

// UpdateProfile 更新用户个人资料
// @Summary 更新个人资料
// @Description 更新当前登录用户的个人资料
// @Tags 用户
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body UpdateProfileRequest true "更新资料请求"
// @Success 200 {object} UserResponse
// @Failure 400 {object} ErrorResponse
// @Router /users/profile [put]
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	var req UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	user, err := h.userService.UpdateUser(userID.(string), &services.UserUpdateRequest{
		FullName:   req.FullName,
		Department: req.Department,
		AvatarURL:  req.AvatarURL,
		Phone:      req.Phone,
		Email:      req.Email,
	})
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "更新资料失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, UserResponse{
		Success: true,
		Message: "更新成功",
		Data:    user,
	})
}

// ActivateStudent 激活学生账号
// @Summary 激活学生账号
// @Description 导师激活学生的账号
// @Tags 用户
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "学生ID"
// @Success 200 {object} SuccessResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /users/activate/{id} [post]
func (h *UserHandler) ActivateStudent(c *gin.Context) {
	studentID := c.Param("id")
	if studentID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "学生ID不能为空",
		})
		return
	}

	activatorID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 这里应该调用认证服务的方法
	// 为了演示，我们直接使用用户服务
	err := h.userService.UpdateUserStatus(studentID, models.UserStatusActive, activatorID.(string))
	if err != nil {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Success: false,
			Message: "激活失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "学生激活成功",
	})
}

// ListUsers 获取用户列表
// @Summary 获取用户列表
// @Description 获取用户列表，支持过滤和分页
// @Tags 用户
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param role query string false "用户角色"
// @Param status query string false "用户状态"
// @Param department query string false "院系"
// @Param research_group_id query string false "课题组ID"
// @Param search query string false "搜索关键词"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} UserListResponse
// @Router /users [get]
func (h *UserHandler) ListUsers(c *gin.Context) {
	// 构建过滤器
	filter := &services.UserFilter{
		Role:            c.Query("role"),
		Status:          c.Query("status"),
		Department:      c.Query("department"),
		ResearchGroupID: c.Query("research_group_id"),
		Search:          c.Query("search"),
	}

	// 分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	users, total, err := h.userService.ListUsers(filter, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Message: "获取用户列表失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, UserListResponse{
		Success: true,
		Message: "获取成功",
		Data: UserListData{
			Users: users,
			Pagination: Pagination{
				Page:     page,
				PageSize: pageSize,
				Total:    int(total),
			},
		},
	})
}

// ListResearchGroups 获取课题组列表
// @Summary 获取课题组列表
// @Description 获取所有课题组列表
// @Tags 用户
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} ResearchGroupListResponse
// @Router /research-groups [get]
func (h *UserHandler) ListResearchGroups(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	groups, total, err := h.userService.ListResearchGroups(page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Message: "获取课题组列表失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, ResearchGroupListResponse{
		Success: true,
		Message: "获取成功",
		Data: ResearchGroupListData{
			Groups: groups,
			Pagination: Pagination{
				Page:     page,
				PageSize: pageSize,
				Total:    int(total),
			},
		},
	})
}

// CreateResearchGroup 创建课题组
// @Summary 创建课题组
// @Description 创建新的课题组
// @Tags 用户
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateResearchGroupRequest true "创建课题组请求"
// @Success 201 {object} ResearchGroupResponse
// @Failure 400 {object} ErrorResponse
// @Router /research-groups [post]
func (h *UserHandler) CreateResearchGroup(c *gin.Context) {
	creatorID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	var req CreateResearchGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	group, err := h.userService.CreateResearchGroup(&services.CreateResearchGroupRequest{
		Name:        req.Name,
		Description: req.Description,
	}, creatorID.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "创建课题组失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, ResearchGroupResponse{
		Success: true,
		Message: "创建成功",
		Data:    group,
	})
}

// UpdateResearchGroup 更新课题组
// @Summary 更新课题组
// @Description 更新课题组信息
// @Tags 用户
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "课题组ID"
// @Param request body UpdateResearchGroupRequest true "更新课题组请求"
// @Success 200 {object} ResearchGroupResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /research-groups/{id} [put]
func (h *UserHandler) UpdateResearchGroup(c *gin.Context) {
	groupID := c.Param("id")
	if groupID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "课题组ID不能为空",
		})
		return
	}

	operatorID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	var req UpdateResearchGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	group, err := h.userService.UpdateResearchGroup(groupID, &services.UpdateResearchGroupRequest{
		Name:        req.Name,
		Description: req.Description,
	}, operatorID.(string))
	if err != nil {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Success: false,
			Message: "更新课题组失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, ResearchGroupResponse{
		Success: true,
		Message: "更新成功",
		Data:    group,
	})
}

// UpdateProfileRequest 更新资料请求
type UpdateProfileRequest struct {
	FullName   *string `json:"full_name,omitempty"`
	Department *string `json:"department,omitempty"`
	AvatarURL  *string `json:"avatar_url,omitempty"`
	Phone      *string `json:"phone,omitempty"`
	Email      *string `json:"email,omitempty"`
}

// CreateResearchGroupRequest 创建课题组请求
type CreateResearchGroupRequest struct {
	Name        string  `json:"name" binding:"required,min=2,max=100"`
	Description *string `json:"description,omitempty"`
}

// UpdateResearchGroupRequest 更新课题组请求
type UpdateResearchGroupRequest struct {
	Name        *string `json:"name,omitempty"`
	Description *string `json:"description,omitempty"`
}

// UserResponse 用户响应
type UserResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    *models.User `json:"data"`
}

// UserListResponse 用户列表响应
type UserListResponse struct {
	Success bool           `json:"success"`
	Message string         `json:"message"`
	Data    UserListData   `json:"data"`
}

// UserListData 用户列表数据
type UserListData struct {
	Users      []*models.User `json:"users"`
	Pagination Pagination     `json:"pagination"`
}

// ResearchGroupResponse 课题组响应
type ResearchGroupResponse struct {
	Success bool                    `json:"success"`
	Message string                  `json:"message"`
	Data    *models.ResearchGroup   `json:"data"`
}

// ResearchGroupListResponse 课题组列表响应
type ResearchGroupListResponse struct {
	Success bool                     `json:"success"`
	Message string                   `json:"message"`
	Data    ResearchGroupListData    `json:"data"`
}

// ResearchGroupListData 课题组列表数据
type ResearchGroupListData struct {
	Groups     []*models.ResearchGroup `json:"groups"`
	Pagination Pagination              `json:"pagination"`
}

// Pagination 分页信息
type Pagination struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
	Total    int `json:"total"`
}


