package handlers

import (
	"net/http"
	"strconv"

	"instrument-management/internal/models"
	"instrument-management/internal/services"

	"github.com/gin-gonic/gin"
)

// InstrumentHandler 仪器处理器
type InstrumentHandler struct {
	instrumentService *services.InstrumentService
}

// NewInstrumentHandler 创建仪器处理器
func NewInstrumentHandler(instrumentService *services.InstrumentService) *InstrumentHandler {
	return &InstrumentHandler{
		instrumentService: instrumentService,
	}
}

// CreateInstrument 创建仪器
// @Summary 创建仪器
// @Description 创建新的仪器设备
// @Tags 仪器
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateInstrumentRequest true "创建仪器请求"
// @Success 201 {object} InstrumentResponse
// @Failure 400 {object} ErrorResponse
// @Router /instruments [post]
func (h *InstrumentHandler) CreateInstrument(c *gin.Context) {
	creatorID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	var req CreateInstrumentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 转换请求为服务层请求
	serviceReq := &services.CreateInstrumentRequest{
		Name:                    req.Name,
		Description:             req.Description,
		Model:                   req.Model,
		Manufacturer:            req.Manufacturer,
		Location:                req.Location,
		ImageURL:                req.ImageURL,
		ControlType:             req.ControlType,
		RequiresTraining:        req.RequiresTraining,
		AcceptsSampleSubmission: req.AcceptsSampleSubmission,
		RequiresReservation:     req.RequiresReservation,
		ResponsibleUserID:       req.ResponsibleUserID,
	}

	// 处理工作时间
	if req.WorkingHours != nil {
		var workingHours []services.CreateWorkingHoursRequest
		for _, wh := range req.WorkingHours {
			workingHours = append(workingHours, services.CreateWorkingHoursRequest{
				DayOfWeek:   wh.DayOfWeek,
				StartTime:   wh.StartTime,
				EndTime:     wh.EndTime,
				IsWorkingDay: wh.IsWorkingDay,
			})
		}
		serviceReq.WorkingHours = workingHours
	}

	// 处理计费配置
	if req.BillingConfig != nil {
		serviceReq.BillingConfig = &services.CreateBillingConfigRequest{
			BillingType:  req.BillingConfig.BillingType,
			BaseRate:     req.BillingConfig.BaseRate,
			Unit:         req.BillingConfig.Unit,
			TagDiscounts: req.BillingConfig.TagDiscounts,
		}
	}

	// 处理用户标签
	if req.UserTags != nil {
		var userTags []services.CreateUserTagRequest
		for _, ut := range req.UserTags {
			userTags = append(userTags, services.CreateUserTagRequest{
				Name:        ut.Name,
				Description: ut.Description,
				Priority:    ut.Priority,
				Discount:    ut.Discount,
			})
		}
		serviceReq.UserTags = userTags
	}

	instrument, err := h.instrumentService.CreateInstrument(serviceReq, creatorID.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "创建仪器失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, InstrumentResponse{
		Success: true,
		Message: "创建成功",
		Data:    instrument,
	})
}

// UpdateInstrument 更新仪器
// @Summary 更新仪器
// @Description 更新仪器信息
// @Tags 仪器
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "仪器ID"
// @Param request body UpdateInstrumentRequest true "更新仪器请求"
// @Success 200 {object} InstrumentResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /instruments/{id} [put]
func (h *InstrumentHandler) UpdateInstrument(c *gin.Context) {
	instrumentID := c.Param("id")
	if instrumentID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "仪器ID不能为空",
		})
		return
	}

	operatorID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	var req UpdateInstrumentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	instrument, err := h.instrumentService.UpdateInstrument(instrumentID, &services.UpdateInstrumentRequest{
		Name:                    req.Name,
		Description:             req.Description,
		Model:                   req.Model,
		Manufacturer:            req.Manufacturer,
		Location:                req.Location,
		ImageURL:                req.ImageURL,
		ControlType:             req.ControlType,
		RequiresTraining:        req.RequiresTraining,
		AcceptsSampleSubmission: req.AcceptsSampleSubmission,
		RequiresReservation:     req.RequiresReservation,
		ResponsibleUserID:       req.ResponsibleUserID,
	}, operatorID.(string))
	if err != nil {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Success: false,
			Message: "更新仪器失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, InstrumentResponse{
		Success: true,
		Message: "更新成功",
		Data:    instrument,
	})
}

// GetInstrument 获取仪器详情
// @Summary 获取仪器详情
// @Description 获取指定仪器的详细信息
// @Tags 仪器
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "仪器ID"
// @Success 200 {object} InstrumentResponse
// @Failure 404 {object} ErrorResponse
// @Router /instruments/{id} [get]
func (h *InstrumentHandler) GetInstrument(c *gin.Context) {
	instrumentID := c.Param("id")
	if instrumentID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "仪器ID不能为空",
		})
		return
	}

	instrument, err := h.instrumentService.GetInstrument(instrumentID)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Success: false,
			Message: "获取仪器失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, InstrumentResponse{
		Success: true,
		Message: "获取成功",
		Data:    instrument,
	})
}

// ListInstruments 获取仪器列表
// @Summary 获取仪器列表
// @Description 获取仪器列表，支持过滤和分页
// @Tags 仪器
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param status query string false "仪器状态"
// @Param responsible_user_id query string false "负责人ID"
// @Param location query string false "位置"
// @Param search query string false "搜索关键词"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} InstrumentListResponse
// @Router /instruments [get]
func (h *InstrumentHandler) ListInstruments(c *gin.Context) {
	// 构建过滤器
	filter := &services.InstrumentFilter{
		Status:            c.Query("status"),
		ResponsibleUserID: c.Query("responsible_user_id"),
		Location:          c.Query("location"),
		Search:            c.Query("search"),
	}

	// 分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	instruments, total, err := h.instrumentService.ListInstruments(filter, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Message: "获取仪器列表失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, InstrumentListResponse{
		Success: true,
		Message: "获取成功",
		Data: InstrumentListData{
			Instruments: instruments,
			Pagination: Pagination{
				Page:     page,
				PageSize: pageSize,
				Total:    int(total),
			},
		},
	})
}

// UpdateInstrumentStatus 更新仪器状态
// @Summary 更新仪器状态
// @Description 更新仪器的运行状态
// @Tags 仪器
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "仪器ID"
// @Param request body UpdateInstrumentStatusRequest true "更新状态请求"
// @Success 200 {object} SuccessResponse
// @Failure 403 {object} ErrorResponse
// @Router /instruments/{id}/status [post]
func (h *InstrumentHandler) UpdateInstrumentStatus(c *gin.Context) {
	instrumentID := c.Param("id")
	if instrumentID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "仪器ID不能为空",
		})
		return
	}

	operatorID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	var req UpdateInstrumentStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	err := h.instrumentService.UpdateInstrumentStatus(instrumentID, models.InstrumentStatus(req.Status), operatorID.(string))
	if err != nil {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Success: false,
			Message: "更新状态失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "更新成功",
	})
}

// DeleteInstrument 删除仪器
// @Summary 删除仪器
// @Description 删除指定的仪器（需要管理员权限）
// @Tags 仪器
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "仪器ID"
// @Success 200 {object} SuccessResponse
// @Failure 403 {object} ErrorResponse
// @Router /instruments/{id} [delete]
func (h *InstrumentHandler) DeleteInstrument(c *gin.Context) {
	instrumentID := c.Param("id")
	if instrumentID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "仪器ID不能为空",
		})
		return
	}

	operatorID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	err := h.instrumentService.DeleteInstrument(instrumentID, operatorID.(string))
	if err != nil {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Success: false,
			Message: "删除仪器失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "删除成功",
	})
}

// CreateInstrumentRequest 创建仪器请求
type CreateInstrumentRequest struct {
	Name                     string                      `json:"name" binding:"required"`
	Description              *string                     `json:"description,omitempty"`
	Model                    string                      `json:"model" binding:"required"`
	Manufacturer             string                      `json:"manufacturer" binding:"required"`
	Location                 string                      `json:"location" binding:"required"`
	ImageURL                 *string                     `json:"image_url,omitempty"`
	ControlType              string                      `json:"control_type" binding:"required"`
	RequiresTraining         bool                        `json:"requires_training"`
	AcceptsSampleSubmission  bool                        `json:"accepts_sample_submission"`
	RequiresReservation      bool                        `json:"requires_reservation"`
	ResponsibleUserID        string                      `json:"responsible_user_id" binding:"required"`
	WorkingHours             []CreateWorkingHoursRequest `json:"working_hours,omitempty"`
	BillingConfig            *CreateBillingConfigRequest `json:"billing_config,omitempty"`
	UserTags                 []CreateUserTagRequest      `json:"user_tags,omitempty"`
}

// UpdateInstrumentRequest 更新仪器请求
type UpdateInstrumentRequest struct {
	Name                    *string `json:"name,omitempty"`
	Description             *string `json:"description,omitempty"`
	Model                   *string `json:"model,omitempty"`
	Manufacturer            *string `json:"manufacturer,omitempty"`
	Location                *string `json:"location,omitempty"`
	ImageURL                *string `json:"image_url,omitempty"`
	ControlType             *string `json:"control_type,omitempty"`
	RequiresTraining        *bool   `json:"requires_training,omitempty"`
	AcceptsSampleSubmission *bool   `json:"accepts_sample_submission,omitempty"`
	RequiresReservation     *bool   `json:"requires_reservation,omitempty"`
	ResponsibleUserID       *string `json:"responsible_user_id,omitempty"`
}

// CreateWorkingHoursRequest 创建工作时间请求
type CreateWorkingHoursRequest struct {
	DayOfWeek   int    `json:"day_of_week" binding:"required,min=1,max=7"`
	StartTime   string `json:"start_time" binding:"required"`
	EndTime     string `json:"end_time" binding:"required"`
	IsWorkingDay bool  `json:"is_working_day"`
}

// CreateBillingConfigRequest 创建计费配置请求
type CreateBillingConfigRequest struct {
	BillingType  string             `json:"billing_type" binding:"required"`
	BaseRate     float64            `json:"base_rate" binding:"required"`
	Unit         string             `json:"unit" binding:"required"`
	TagDiscounts map[string]float64 `json:"tag_discounts,omitempty"`
}

// CreateUserTagRequest 创建用户标签请求
type CreateUserTagRequest struct {
	Name        string  `json:"name" binding:"required"`
	Description *string `json:"description,omitempty"`
	Priority    float64 `json:"priority"`
	Discount    float64 `json:"discount"`
}

// UpdateInstrumentStatusRequest 更新仪器状态请求
type UpdateInstrumentStatusRequest struct {
	Status string `json:"status" binding:"required"`
}

// InstrumentResponse 仪器响应
type InstrumentResponse struct {
	Success bool                `json:"success"`
	Message string              `json:"message"`
	Data    *models.Instrument  `json:"data"`
}

// InstrumentListResponse 仪器列表响应
type InstrumentListResponse struct {
	Success bool                   `json:"success"`
	Message string                 `json:"message"`
	Data    InstrumentListData     `json:"data"`
}

// InstrumentListData 仪器列表数据
type InstrumentListData struct {
	Instruments []*models.Instrument `json:"instruments"`
	Pagination  Pagination           `json:"pagination"`
}












