package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Reservation 预约基础模型
type Reservation struct {
	ID             string            `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	InstrumentID   string            `json:"instrument_id" gorm:"type:uuid;not null"`
	UserID         string            `json:"user_id" gorm:"type:uuid;not null"`
	Type           ReservationType   `json:"type" gorm:"type:varchar(20);not null"`
	Status         ReservationStatus `json:"status" gorm:"type:varchar(20);not null;default:'pending'"`
	StartTime      time.Time         `json:"start_time" gorm:"not null"`
	EndTime        time.Time         `json:"end_time" gorm:"not null"`
	Notes          *string           `json:"notes,omitempty" gorm:"type:text"`
	ApprovedAt     *time.Time        `json:"approved_at,omitempty"`
	RejectedAt     *time.Time        `json:"rejected_at,omitempty"`
	ApprovedBy     *string           `json:"approved_by,omitempty" gorm:"type:uuid"`
	RejectedBy     *string           `json:"rejected_by,omitempty" gorm:"type:uuid"`
	RejectReason   *string           `json:"reject_reason,omitempty" gorm:"type:text"`
	CreatedAt      time.Time         `json:"created_at"`
	UpdatedAt      time.Time         `json:"updated_at"`

	// 关联
	Instrument     Instrument        `json:"instrument" gorm:"foreignKey:InstrumentID"`
	User           User              `json:"user" gorm:"foreignKey:UserID"`
	Approver       *User             `json:"approver,omitempty" gorm:"foreignKey:ApprovedBy"`
	Rejector       *User             `json:"rejector,omitempty" gorm:"foreignKey:RejectedBy"`
}

// ReservationType 预约类型枚举
type ReservationType string

const (
	ReservationTypeSelf   ReservationType = "self_reservation"
	ReservationTypeSample ReservationType = "sample_submission"
)

// ReservationStatus 预约状态枚举
type ReservationStatus string

const (
	ReservationStatusPending    ReservationStatus = "pending"
	ReservationStatusApproved   ReservationStatus = "approved"
	ReservationStatusRejected   ReservationStatus = "rejected"
	ReservationStatusCancelled  ReservationStatus = "cancelled"
	ReservationStatusCompleted  ReservationStatus = "completed"
	ReservationStatusInProgress ReservationStatus = "in_progress"
)

// SelfReservation 自主预约模型
type SelfReservation struct {
	Reservation
	TrainingRecordID *string `json:"training_record_id,omitempty" gorm:"type:uuid"`
	FormData         string  `json:"form_data" gorm:"type:jsonb"` // JSON格式存储表单数据
}

// SampleSubmission 送样预约模型
type SampleSubmission struct {
	Reservation
	SampleName         string   `json:"sample_name" gorm:"not null"`
	SampleDescription  *string  `json:"sample_description,omitempty" gorm:"type:text"`
	SampleCount        int      `json:"sample_count" gorm:"default:1"`
	Attachments        string   `json:"attachments" gorm:"type:jsonb"` // JSON数组格式存储附件URL
	TestReportURL      *string  `json:"test_report_url,omitempty" gorm:"type:text"`
	CompletedAt        *time.Time `json:"completed_at,omitempty"`
	SampleStatus       SampleStatus `json:"sample_status" gorm:"type:varchar(20);default:'pending'"`
}

// SampleStatus 样品状态枚举
type SampleStatus string

const (
	SampleStatusPending   SampleStatus = "pending"
	SampleStatusReceived  SampleStatus = "received"
	SampleStatusTesting   SampleStatus = "testing"
	SampleStatusCompleted SampleStatus = "completed"
	SampleStatusFailed    SampleStatus = "failed"
)

// UsageRecord 使用记录模型
type UsageRecord struct {
	ID             string        `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ReservationID  string        `json:"reservation_id" gorm:"type:uuid;not null"`
	InstrumentID   string        `json:"instrument_id" gorm:"type:uuid;not null"`
	UserID         string        `json:"user_id" gorm:"type:uuid;not null"`
	ActualStartTime time.Time    `json:"actual_start_time" gorm:"not null"`
	ActualEndTime  *time.Time    `json:"actual_end_time,omitempty"`
	ActualDuration float64       `json:"actual_duration" gorm:"type:decimal(8,2);not null"` // 实际使用时长（小时）
	ChargeAmount   float64       `json:"charge_amount" gorm:"type:decimal(10,2);not null"`
	BillingType    BillingType   `json:"billing_type" gorm:"type:varchar(20);not null"`
	Notes          *string       `json:"notes,omitempty" gorm:"type:text"`
	CreatedAt      time.Time     `json:"created_at"`
	UpdatedAt      time.Time     `json:"updated_at"`

	// 关联
	Reservation    Reservation   `json:"reservation" gorm:"foreignKey:ReservationID"`
	Instrument     Instrument    `json:"instrument" gorm:"foreignKey:InstrumentID"`
	User           User          `json:"user" gorm:"foreignKey:UserID"`
}

// TrainingRecord 培训记录模型
type TrainingRecord struct {
	ID           string          `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID       string          `json:"user_id" gorm:"type:uuid;not null"`
	InstrumentID string          `json:"instrument_id" gorm:"type:uuid;not null"`
	TrainerID    string          `json:"trainer_id" gorm:"type:uuid;not null"`
	TrainingDate time.Time       `json:"training_date" gorm:"not null"`
	Duration     int             `json:"duration" gorm:"not null"` // 培训时长（分钟）
	Status       TrainingStatus  `json:"status" gorm:"type:varchar(20);not null;default:'pending'"`
	Notes        *string         `json:"notes,omitempty" gorm:"type:text"`
	ExpiryDate   *time.Time      `json:"expiry_date,omitempty"`
	CreatedAt    time.Time       `json:"created_at"`
	UpdatedAt    time.Time       `json:"updated_at"`

	// 关联
	User          User            `json:"user" gorm:"foreignKey:UserID"`
	Instrument    Instrument      `json:"instrument" gorm:"foreignKey:InstrumentID"`
	Trainer       User            `json:"trainer" gorm:"foreignKey:TrainerID"`
}

// TrainingStatus 培训状态枚举
type TrainingStatus string

const (
	TrainingStatusPending   TrainingStatus = "pending"
	TrainingStatusScheduled TrainingStatus = "scheduled"
	TrainingStatusCompleted TrainingStatus = "completed"
	TrainingStatusExpired   TrainingStatus = "expired"
	TrainingStatusFailed    TrainingStatus = "failed"
)

// BeforeCreate GORM钩子：在创建前生成UUID
func (r *Reservation) BeforeCreate(tx *gorm.DB) error {
	if r.ID == "" {
		r.ID = uuid.New().String()
	}
	return nil
}

func (ur *UsageRecord) BeforeCreate(tx *gorm.DB) error {
	if ur.ID == "" {
		ur.ID = uuid.New().String()
	}
	return nil
}

func (tr *TrainingRecord) BeforeCreate(tx *gorm.DB) error {
	if tr.ID == "" {
		tr.ID = uuid.New().String()
	}
	return nil
}

// TableName 指定表名
func (Reservation) TableName() string {
	return "reservations"
}

func (SelfReservation) TableName() string {
	return "reservations"
}

func (SampleSubmission) TableName() string {
	return "reservations"
}

func (UsageRecord) TableName() string {
	return "usage_records"
}

func (TrainingRecord) TableName() string {
	return "training_records"
}












