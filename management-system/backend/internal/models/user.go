package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	ID             string         `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Username       string         `json:"username" gorm:"uniqueIndex;not null"`
	Email          string         `json:"email" gorm:"uniqueIndex;not null"`
	Phone          string         `json:"phone" gorm:"not null"`
	PasswordHash   string         `json:"-" gorm:"not null"` // 密码哈希，不在JSON中返回
	Role           UserRole       `json:"role" gorm:"type:varchar(20);not null"`
	Status         UserStatus     `json:"status" gorm:"type:varchar(20);not null;default:'pending'"`
	AvatarURL      *string        `json:"avatar_url,omitempty" gorm:"type:text"`
	FullName       *string        `json:"full_name,omitempty" gorm:"type:varchar(100)"`
	Department     *string        `json:"department,omitempty" gorm:"type:varchar(100)"`
	ResearchGroupID *string       `json:"research_group_id,omitempty" gorm:"type:uuid"`
	ActivatedAt    *time.Time     `json:"activated_at,omitempty"`
	LastLoginAt    *time.Time     `json:"last_login_at,omitempty"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`

	// 关联
	ResearchGroup  *ResearchGroup `json:"research_group,omitempty" gorm:"foreignKey:ResearchGroupID"`
}

// UserRole 用户角色枚举
type UserRole string

const (
	UserRoleStudent    UserRole = "student"
	UserRoleTeacher    UserRole = "teacher"
	UserRoleTechnician UserRole = "technician"
	UserRoleAdmin      UserRole = "admin"
)

// UserStatus 用户状态枚举
type UserStatus string

const (
	UserStatusPending   UserStatus = "pending"
	UserStatusActive    UserStatus = "active"
	UserStatusInactive  UserStatus = "inactive"
	UserStatusSuspended UserStatus = "suspended"
)

// BeforeCreate GORM钩子：在创建前生成UUID
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.ID == "" {
		u.ID = uuid.New().String()
	}
	return nil
}

// ResearchGroup 课题组模型
type ResearchGroup struct {
	ID          string    `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string    `json:"name" gorm:"not null"`
	Description *string   `json:"description,omitempty" gorm:"type:text"`
	LeaderID    string    `json:"leader_id" gorm:"type:uuid;not null"`
	Balance     float64   `json:"balance" gorm:"type:decimal(10,2);default:0"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// 关联
	Leader User   `json:"leader" gorm:"foreignKey:LeaderID"`
	Members []User `json:"members,omitempty" gorm:"many2many:research_group_members;"`
}

// BeforeCreate GORM钩子：在创建前生成UUID
func (rg *ResearchGroup) BeforeCreate(tx *gorm.DB) error {
	if rg.ID == "" {
		rg.ID = uuid.New().String()
	}
	return nil
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

func (ResearchGroup) TableName() string {
	return "research_groups"
}












