package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Instrument 仪器模型
type Instrument struct {
	ID                        string             `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name                      string             `json:"name" gorm:"not null"`
	Description               *string            `json:"description,omitempty" gorm:"type:text"`
	Model                     string             `json:"model" gorm:"not null"`
	Manufacturer              string             `json:"manufacturer" gorm:"not null"`
	Location                  string             `json:"location" gorm:"not null"`
	ImageURL                  *string            `json:"image_url,omitempty" gorm:"type:text"`
	Status                    InstrumentStatus   `json:"status" gorm:"type:varchar(20);not null;default:'offline'"`
	ControlType               InstrumentControlType `json:"control_type" gorm:"type:varchar(20);not null;default:'manual'"`
	RequiresTraining          bool               `json:"requires_training" gorm:"default:false"`
	AcceptsSampleSubmission   bool               `json:"accepts_sample_submission" gorm:"default:false"`
	RequiresReservation       bool               `json:"requires_reservation" gorm:"default:false"`
	ResponsibleUserID         string             `json:"responsible_user_id" gorm:"type:uuid;not null"`
	LastMaintenanceDate       *time.Time         `json:"last_maintenance_date,omitempty"`
	CreatedAt                 time.Time          `json:"created_at"`
	UpdatedAt                 time.Time          `json:"updated_at"`

	// 关联
	ResponsibleUser           User               `json:"responsible_user" gorm:"foreignKey:ResponsibleUserID"`
	WorkingHours              []WorkingHours     `json:"working_hours,omitempty" gorm:"foreignKey:InstrumentID"`
	BillingConfig             *BillingConfig     `json:"billing_config,omitempty" gorm:"foreignKey:InstrumentID"`
	UserTags                  []UserTag          `json:"user_tags,omitempty" gorm:"foreignKey:InstrumentID"`
}

// InstrumentStatus 仪器状态枚举
type InstrumentStatus string

const (
	InstrumentStatusOnline       InstrumentStatus = "online"
	InstrumentStatusOffline      InstrumentStatus = "offline"
	InstrumentStatusInUse        InstrumentStatus = "in_use"
	InstrumentStatusMaintenance  InstrumentStatus = "maintenance"
	InstrumentStatusFault        InstrumentStatus = "fault"
	InstrumentStatusScrapped     InstrumentStatus = "scrapped"
)

// InstrumentControlType 仪器控制类型枚举
type InstrumentControlType string

const (
	InstrumentControlManual    InstrumentControlType = "manual"
	InstrumentControlPower     InstrumentControlType = "power"
	InstrumentControlComputer  InstrumentControlType = "computer"
	InstrumentControlAutomated InstrumentControlType = "automated"
)

// WorkingHours 工作时间配置
type WorkingHours struct {
	ID           string `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	InstrumentID string `json:"instrument_id" gorm:"type:uuid;not null"`
	DayOfWeek    int    `json:"day_of_week" gorm:"not null"` // 1-7 (周一到周日)
	StartTime    string `json:"start_time" gorm:"type:varchar(5);not null"` // HH:mm格式
	EndTime      string `json:"end_time" gorm:"type:varchar(5);not null"`   // HH:mm格式
	IsWorkingDay bool   `json:"is_working_day" gorm:"default:true"`
}

// BillingConfig 计费配置
type BillingConfig struct {
	ID              string         `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	InstrumentID    string         `json:"instrument_id" gorm:"type:uuid;not null"`
	BillingType     BillingType    `json:"billing_type" gorm:"type:varchar(20);not null;default:'per_hour'"`
	BaseRate        float64        `json:"base_rate" gorm:"type:decimal(10,2);not null"`
	Unit            string         `json:"unit" gorm:"type:varchar(20);not null"`
	TagDiscounts    string         `json:"tag_discounts" gorm:"type:jsonb"` // JSON格式存储折扣配置
}

// BillingType 计费类型枚举
type BillingType string

const (
	BillingTypePerHour   BillingType = "per_hour"
	BillingTypePerSample BillingType = "per_sample"
	BillingTypeFlatRate  BillingType = "flat_rate"
)

// UserTag 用户标签
type UserTag struct {
	ID          string  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	InstrumentID string  `json:"instrument_id" gorm:"type:uuid;not null"`
	Name        string  `json:"name" gorm:"not null"`
	Description *string `json:"description,omitempty" gorm:"type:text"`
	Priority    float64 `json:"priority" gorm:"type:decimal(3,2);default:0"` // 优先级权重 0-1
	Discount    float64 `json:"discount" gorm:"type:decimal(3,2);default:0"` // 折扣比例 0-1
}

// BeforeCreate GORM钩子：在创建前生成UUID
func (i *Instrument) BeforeCreate(tx *gorm.DB) error {
	if i.ID == "" {
		i.ID = uuid.New().String()
	}
	return nil
}

func (wh *WorkingHours) BeforeCreate(tx *gorm.DB) error {
	if wh.ID == "" {
		wh.ID = uuid.New().String()
	}
	return nil
}

func (bc *BillingConfig) BeforeCreate(tx *gorm.DB) error {
	if bc.ID == "" {
		bc.ID = uuid.New().String()
	}
	return nil
}

func (ut *UserTag) BeforeCreate(tx *gorm.DB) error {
	if ut.ID == "" {
		ut.ID = uuid.New().String()
	}
	return nil
}

// TableName 指定表名
func (Instrument) TableName() string {
	return "instruments"
}

func (WorkingHours) TableName() string {
	return "working_hours"
}

func (BillingConfig) TableName() string {
	return "billing_configs"
}

func (UserTag) TableName() string {
	return "user_tags"
}












