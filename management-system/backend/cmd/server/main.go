package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"instrument-management/internal/config"
	"instrument-management/internal/database"
	"instrument-management/internal/handlers"
	"instrument-management/internal/middleware"
	"instrument-management/internal/services"
	"instrument-management/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
)

func main() {
	// 初始化配置
	if err := config.Init(); err != nil {
		log.Fatalf("配置初始化失败: %v", err)
	}

	// 初始化日志
	if err := logger.Init(); err != nil {
		log.Fatalf("日志初始化失败: %v", err)
	}

	// 连接数据库
	db, err := database.Init()
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}

	// 初始化服务
	authService := services.NewAuthService(db)
	userService := services.NewUserService(db)
	instrumentService := services.NewInstrumentService(db)
	reservationService := services.NewReservationService(db)
	billingService := services.NewBillingService(db)

	// 初始化处理器
	authHandler := handlers.NewAuthHandler(authService, userService)
	userHandler := handlers.NewUserHandler(userService)
	instrumentHandler := handlers.NewInstrumentHandler(instrumentService)
	reservationHandler := handlers.NewReservationHandler(reservationService)
	billingHandler := handlers.NewBillingHandler(billingService)

	// 设置Gin模式
	if viper.GetString("app.env") == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建Gin引擎
	r := gin.New()

	// 全局中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())
	r.Use(middleware.CORS())
	r.Use(middleware.RequestID())

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"timestamp": "",
		})
	})

	// API路由组
	api := r.Group("/api/v1")
	{
		// 认证路由
		auth := api.Group("/auth")
		{
			auth.POST("/sso/login", authHandler.SSOLogin)
			auth.POST("/register/student", authHandler.RegisterStudent)
			auth.POST("/register/teacher", authHandler.RegisterTeacher)
			auth.POST("/refresh", authHandler.RefreshToken)
			auth.POST("/logout", authHandler.Logout)
		}

		// 需要认证的路由
		authorized := api.Group("")
		authorized.Use(middleware.JWTAuth())
		{
			// 用户相关
			users := authorized.Group("/users")
			{
				users.GET("/profile", userHandler.GetProfile)
				users.PUT("/profile", userHandler.UpdateProfile)
				users.POST("/activate/:id", userHandler.ActivateStudent)
				users.GET("", userHandler.ListUsers)
			}

			// 课题组相关
			researchGroups := authorized.Group("/research-groups")
			{
				researchGroups.GET("", userHandler.ListResearchGroups)
				researchGroups.POST("", userHandler.CreateResearchGroup)
				researchGroups.PUT("/:id", userHandler.UpdateResearchGroup)
			}

			// 仪器相关
			instruments := authorized.Group("/instruments")
			{
				instruments.GET("", instrumentHandler.ListInstruments)
				instruments.POST("", instrumentHandler.CreateInstrument)
				instruments.GET("/:id", instrumentHandler.GetInstrument)
				instruments.PUT("/:id", instrumentHandler.UpdateInstrument)
				instruments.DELETE("/:id", instrumentHandler.DeleteInstrument)
				instruments.POST("/:id/status", instrumentHandler.UpdateInstrumentStatus)
			}

			// 预约相关
			reservations := authorized.Group("/reservations")
			{
				reservations.GET("", reservationHandler.ListReservations)
				reservations.POST("/self", reservationHandler.CreateSelfReservation)
				reservations.POST("/sample", reservationHandler.CreateSampleSubmission)
				reservations.GET("/:id", reservationHandler.GetReservation)
				reservations.PUT("/:id", reservationHandler.UpdateReservation)
				reservations.DELETE("/:id", reservationHandler.CancelReservation)
				reservations.POST("/:id/approve", reservationHandler.ApproveReservation)
				reservations.POST("/:id/reject", reservationHandler.RejectReservation)
			}

			// 计费相关
			billing := authorized.Group("/billing")
			{
				billing.GET("/calculate/:id", billingHandler.CalculateCharge)
				billing.POST("/transactions", billingHandler.CreateTransaction)
				billing.GET("/transactions", billingHandler.ListTransactions)
				billing.POST("/deposit", billingHandler.CreateDeposit)
				billing.POST("/bills", billingHandler.CreateBill)
				billing.GET("/bills", billingHandler.ListBills)
				billing.POST("/bills/:id/pay", billingHandler.PayBill)
				billing.GET("/stats", billingHandler.GetFinancialStats)
			}

			// 统计相关
			stats := authorized.Group("/stats")
			{
				stats.GET("/dashboard", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Dashboard stats coming soon"})
				})
				stats.GET("/instruments", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Instrument stats coming soon"})
				})
				stats.GET("/usage", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Usage stats coming soon"})
				})
			}
		}
	}

	// 获取服务器配置
	port := viper.GetString("server.port")
	if port == "" {
		port = "8080"
	}

	host := viper.GetString("server.host")
	if host == "" {
		host = "localhost"
	}

	addr := host + ":" + port

	logger.Info("服务器启动", "address", addr)

	// 启动服务器
	go func() {
		if err := r.Run(addr); err != nil {
			logger.Error("服务器启动失败", err)
			os.Exit(1)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("服务器关闭")

	// 优雅关闭
	if err := database.Close(); err != nil {
		logger.Error("数据库关闭失败", err)
	}
}
