-- 西北工业大学大型仪器共享管理系统 - 数据库初始化脚本
-- 创建数据库和基础用户

-- 创建用户角色
CREATE TYPE user_role AS ENUM ('admin', 'teacher', 'student');
CREATE TYPE user_status AS ENUM ('pending', 'active', 'inactive', 'banned');

-- 创建仪器状态
CREATE TYPE instrument_status AS ENUM ('online', 'offline', 'in_use', 'maintenance', 'fault', 'decommissioned');
CREATE TYPE instrument_control_type AS ENUM ('power_only', 'computer', 'automated');

-- 创建预约类型和状态
CREATE TYPE reservation_type AS ENUM ('self_reservation', 'sample_submission');
CREATE TYPE reservation_status AS ENUM ('pending', 'approved', 'rejected', 'in_progress', 'completed', 'cancelled');

-- 创建计费类型和状态
CREATE TYPE billing_type AS ENUM ('per_hour', 'per_sample', 'fixed_fee');
CREATE TYPE transaction_status AS ENUM ('pending', 'completed', 'failed', 'refunded');
CREATE TYPE bill_status AS ENUM ('unpaid', 'paid', 'overdue', 'cancelled');

-- 创建用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    role user_role NOT NULL DEFAULT 'student',
    status user_status NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建课题组表
CREATE TABLE research_groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    leader_id UUID REFERENCES users(id),
    balance DECIMAL(12,2) DEFAULT 0.00,
    credit_limit DECIMAL(12,2) DEFAULT 10000.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建仪器表
CREATE TABLE instruments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    model VARCHAR(100),
    manufacturer VARCHAR(100),
    location VARCHAR(200),
    status instrument_status DEFAULT 'online',
    control_type instrument_control_type DEFAULT 'computer',
    requires_training BOOLEAN DEFAULT false,
    accepts_sample_submission BOOLEAN DEFAULT false,
    requires_reservation BOOLEAN DEFAULT true,
    responsible_user_id UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建预约表
CREATE TABLE reservations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    instrument_id UUID NOT NULL REFERENCES instruments(id),
    user_id UUID NOT NULL REFERENCES users(id),
    research_group_id UUID REFERENCES research_groups(id),
    type reservation_type DEFAULT 'self_reservation',
    status reservation_status DEFAULT 'pending',
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    notes TEXT,
    approved_by UUID REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建培训记录表
CREATE TABLE training_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    instrument_id UUID NOT NULL REFERENCES instruments(id),
    trainer_id UUID NOT NULL REFERENCES users(id),
    training_date TIMESTAMP WITH TIME ZONE NOT NULL,
    certificate_issued BOOLEAN DEFAULT false,
    certificate_number VARCHAR(100) UNIQUE,
    expiry_date TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建交易记录表
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    reservation_id UUID REFERENCES reservations(id),
    user_id UUID NOT NULL REFERENCES users(id),
    research_group_id UUID REFERENCES research_groups(id),
    amount DECIMAL(10,2) NOT NULL,
    billing_type billing_type DEFAULT 'per_hour',
    description VARCHAR(500),
    status transaction_status DEFAULT 'pending',
    transaction_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建账单表
CREATE TABLE bills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    research_group_id UUID REFERENCES research_groups(id),
    total_amount DECIMAL(10,2) NOT NULL,
    paid_amount DECIMAL(10,2) DEFAULT 0.00,
    status bill_status DEFAULT 'unpaid',
    due_date TIMESTAMP WITH TIME ZONE,
    paid_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);

CREATE INDEX idx_instruments_status ON instruments(status);
CREATE INDEX idx_instruments_location ON instruments(location);
CREATE INDEX idx_instruments_responsible_user ON instruments(responsible_user_id);

CREATE INDEX idx_reservations_instrument ON reservations(instrument_id);
CREATE INDEX idx_reservations_user ON reservations(user_id);
CREATE INDEX idx_reservations_status ON reservations(status);
CREATE INDEX idx_reservations_time ON reservations(start_time, end_time);

CREATE INDEX idx_transactions_user ON transactions(user_id);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_time ON transactions(transaction_time);

CREATE INDEX idx_bills_user ON bills(user_id);
CREATE INDEX idx_bills_status ON bills(status);

-- 插入默认管理员用户
-- 密码: admin123 (使用bcrypt哈希)
INSERT INTO users (id, username, email, password_hash, full_name, role, status)
VALUES (
    '550e8400-e29b-41d4-a716-446655440000',
    'admin',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- admin123
    '系统管理员',
    'admin',
    'active'
);

-- 插入默认课题组
INSERT INTO research_groups (id, name, description, leader_id, balance, credit_limit)
VALUES (
    '550e8400-e29b-41d4-a716-446655440001',
    '材料科学研究所',
    '西北工业大学材料科学研究所',
    '550e8400-e29b-41d4-a716-446655440000',
    5000.00,
    20000.00
);

-- 插入示例仪器
INSERT INTO instruments (id, name, description, model, manufacturer, location, status, control_type, requires_training, accepts_sample_submission, responsible_user_id)
VALUES (
    '550e8400-e29b-41d4-a716-446655440002',
    '扫描电子显微镜',
    '高分辨率扫描电子显微镜，用于材料微观结构分析',
    'SEM-1000',
    'NPU Instruments',
    '理学院B101',
    'online',
    'computer',
    true,
    false,
    '550e8400-e29b-41d4-a716-446655440000'
);

INSERT INTO instruments (id, name, description, model, manufacturer, location, status, control_type, requires_training, accepts_sample_submission, responsible_user_id)
VALUES (
    '550e8400-e29b-41d4-a716-446655440003',
    'X射线衍射仪',
    '用于晶体结构分析的X射线衍射仪',
    'XRD-2000',
    'NPU Instruments',
    '材料学院A203',
    'online',
    'automated',
    true,
    true,
    '550e8400-e29b-41d4-a716-446655440000'
);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表添加更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_research_groups_updated_at BEFORE UPDATE ON research_groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_instruments_updated_at BEFORE UPDATE ON instruments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reservations_updated_at BEFORE UPDATE ON reservations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bills_updated_at BEFORE UPDATE ON bills FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();












