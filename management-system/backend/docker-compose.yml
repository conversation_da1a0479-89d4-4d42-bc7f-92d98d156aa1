version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: instrument_management_postgres
    environment:
      POSTGRES_DB: instrument_management
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - instrument_management

  # Redis缓存（可选）
  redis:
    image: redis:7-alpine
    container_name: instrument_management_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - instrument_management
    command: redis-server --appendonly yes

  # 应用程序
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: instrument_management_app
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=password
      - DB_DATABASE=instrument_management
      - DB_SSL_MODE=disable
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - APP_ENV=development
    ports:
      - "8080:8080"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./config.yaml:/root/config.yaml:ro
      - ./logs:/root/logs
      - ./uploads:/root/uploads
    networks:
      - instrument_management
    restart: unless-stopped

  # PgAdmin（数据库管理工具）
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: instrument_management_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - instrument_management
    volumes:
      - pgadmin_data:/var/lib/pgadmin

volumes:
  postgres_data:
  redis_data:
  pgadmin_data:

networks:
  instrument_management:
    driver: bridge


