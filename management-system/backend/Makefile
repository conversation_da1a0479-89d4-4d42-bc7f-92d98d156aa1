# 西北工业大学仪器共享管理系统 - 后端构建和部署

.PHONY: build run test clean migrate docker-build docker-run

# 构建应用
build:
	go build -o bin/server cmd/server/main.go

# 运行应用
run:
	go run cmd/server/main.go

# 运行测试
test:
	go test -v ./...

# 清理构建文件
clean:
	rm -rf bin/

# 数据库迁移
migrate:
	go run scripts/migrate.go

# 创建新的迁移文件
migration:
	@echo "请输入迁移文件名 (例如: add_user_permissions):"
	@read name; \
	version=$$(date +%Y%m%d%H%M%S); \
	echo "创建迁移文件: $${version}_$${name}"; \
	touch migrations/$${version}_$${name}.up.sql; \
	touch migrations/$${version}_$${name}.down.sql; \
	echo "-- 迁移: $${name}" > migrations/$${version}_$${name}.up.sql; \
	echo "-- 回滚: $${name}" > migrations/$${version}_$${name}.down.sql

# 安装依赖
deps:
	go mod download
	go mod tidy

# 格式化代码
fmt:
	go fmt ./...

# 代码检查
vet:
	go vet ./...

# 运行所有检查
check: fmt vet test

# Docker构建
docker-build:
	docker build -t instrument-management .

# Docker运行
docker-run:
	docker run -p 8080:8080 --env-file .env instrument-management

# 开发环境设置
dev-setup: deps
	cp config.yaml.example config.yaml
	@echo "请编辑 config.yaml 文件配置数据库连接信息"

# 生产环境部署
deploy: build
	@echo "部署到生产环境..."
	# 这里可以添加生产环境的部署命令

# 显示帮助信息
help:
	@echo "西北工业大学仪器共享管理系统 - 后端"
	@echo ""
	@echo "可用命令:"
	@echo "  build          - 构建应用"
	@echo "  run            - 运行应用"
	@echo "  test           - 运行测试"
	@echo "  clean          - 清理构建文件"
	@echo "  migrate        - 执行数据库迁移"
	@echo "  migration      - 创建新的迁移文件"
	@echo "  deps           - 安装依赖"
	@echo "  fmt            - 格式化代码"
	@echo "  vet            - 代码检查"
	@echo "  check          - 运行所有检查"
	@echo "  docker-build   - Docker构建"
	@echo "  docker-run     - Docker运行"
	@echo "  dev-setup      - 开发环境设置"
	@echo "  deploy         - 生产环境部署"
	@echo "  help           - 显示此帮助信息"










