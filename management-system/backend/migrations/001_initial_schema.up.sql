-- 初始化数据库表结构
-- 创建时间: 2024-01-15

-- 启用UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(11) NOT NULL,
    password_hash TEXT NOT NULL,
    role VARCHAR(20) NOT NULL DEFAULT 'student' CHECK (role IN ('student', 'teacher', 'technician', 'admin')),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'inactive', 'suspended')),
    avatar_url TEXT,
    full_name VA<PERSON>HA<PERSON>(100),
    department VARCHAR(100),
    research_group_id UUID,
    activated_at TIMESTAMP WITH TIME ZONE,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- 课题组表
CREATE TABLE research_groups (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    leader_id UUID NOT NULL,
    balance DECIMAL(10,2) NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- 仪器表
CREATE TABLE instruments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    model VARCHAR(100) NOT NULL,
    manufacturer VARCHAR(100) NOT NULL,
    location VARCHAR(100) NOT NULL,
    image_url TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'offline' CHECK (status IN ('online', 'offline', 'in_use', 'maintenance', 'fault', 'scrapped')),
    control_type VARCHAR(20) NOT NULL DEFAULT 'manual' CHECK (control_type IN ('manual', 'power', 'computer', 'automated')),
    requires_training BOOLEAN NOT NULL DEFAULT false,
    accepts_sample_submission BOOLEAN NOT NULL DEFAULT false,
    requires_reservation BOOLEAN NOT NULL DEFAULT false,
    responsible_user_id UUID NOT NULL,
    last_maintenance_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- 工作时间表
CREATE TABLE working_hours (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    instrument_id UUID NOT NULL,
    day_of_week INTEGER NOT NULL CHECK (day_of_week BETWEEN 1 AND 7),
    start_time VARCHAR(5) NOT NULL, -- HH:mm格式
    end_time VARCHAR(5) NOT NULL,   -- HH:mm格式
    is_working_day BOOLEAN NOT NULL DEFAULT true
);

-- 计费配置表
CREATE TABLE billing_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    instrument_id UUID NOT NULL UNIQUE,
    billing_type VARCHAR(20) NOT NULL DEFAULT 'per_hour' CHECK (billing_type IN ('per_hour', 'per_sample', 'flat_rate')),
    base_rate DECIMAL(10,2) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    tag_discounts JSONB NOT NULL DEFAULT '{}'
);

-- 用户标签表
CREATE TABLE user_tags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    instrument_id UUID NOT NULL,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    priority DECIMAL(3,2) NOT NULL DEFAULT 0 CHECK (priority >= 0 AND priority <= 1),
    discount DECIMAL(3,2) NOT NULL DEFAULT 0 CHECK (discount >= 0 AND discount <= 1)
);

-- 预约表
CREATE TABLE reservations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    instrument_id UUID NOT NULL,
    user_id UUID NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('self_reservation', 'sample_submission')),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled', 'completed', 'in_progress')),
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    notes TEXT,
    approved_at TIMESTAMP WITH TIME ZONE,
    rejected_at TIMESTAMP WITH TIME ZONE,
    approved_by UUID,
    rejected_by UUID,
    reject_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- 使用记录表
CREATE TABLE usage_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    reservation_id UUID NOT NULL,
    instrument_id UUID NOT NULL,
    user_id UUID NOT NULL,
    actual_start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    actual_end_time TIMESTAMP WITH TIME ZONE,
    actual_duration DECIMAL(8,2) NOT NULL, -- 实际使用时长（小时）
    charge_amount DECIMAL(10,2) NOT NULL,
    billing_type VARCHAR(20) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- 培训记录表
CREATE TABLE training_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    instrument_id UUID NOT NULL,
    trainer_id UUID NOT NULL,
    training_date DATE NOT NULL,
    duration INTEGER NOT NULL, -- 培训时长（分钟）
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'scheduled', 'completed', 'expired', 'failed')),
    notes TEXT,
    expiry_date DATE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- 交易记录表
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    research_group_id UUID NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('charge', 'refund', 'deposit', 'adjustment')),
    amount DECIMAL(10,2) NOT NULL,
    description TEXT NOT NULL,
    related_reservation_id UUID,
    related_instrument_id UUID,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled', 'refunded')),
    transaction_date TIMESTAMP WITH TIME ZONE NOT NULL,
    payment_method VARCHAR(50),
    transaction_id VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- 账单表
CREATE TABLE bills (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    research_group_id UUID NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'unpaid' CHECK (status IN ('unpaid', 'paid', 'overdue', 'cancelled')),
    bill_date DATE NOT NULL,
    due_date DATE,
    paid_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- 账单项目表
CREATE TABLE bill_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bill_id UUID NOT NULL,
    instrument_name VARCHAR(100) NOT NULL,
    reservation_id UUID NOT NULL,
    usage_date DATE NOT NULL,
    duration DECIMAL(8,2) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    quantity DECIMAL(10,2) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    discount DECIMAL(3,2) NOT NULL DEFAULT 0,
    notes TEXT
);

-- 充值记录表
CREATE TABLE deposit_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    research_group_id UUID NOT NULL,
    operator_id UUID NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    transaction_id VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    notes TEXT,
    deposit_date TIMESTAMP WITH TIME ZONE NOT NULL,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_research_group_id ON users(research_group_id);
CREATE INDEX idx_users_status ON users(status);

CREATE INDEX idx_research_groups_leader_id ON research_groups(leader_id);

CREATE INDEX idx_instruments_status ON instruments(status);
CREATE INDEX idx_instruments_responsible_user_id ON instruments(responsible_user_id);
CREATE INDEX idx_instruments_location ON instruments(location);

CREATE INDEX idx_working_hours_instrument_id ON working_hours(instrument_id);

CREATE INDEX idx_billing_configs_instrument_id ON billing_configs(instrument_id);

CREATE INDEX idx_user_tags_instrument_id ON user_tags(instrument_id);

CREATE INDEX idx_reservations_instrument_id ON reservations(instrument_id);
CREATE INDEX idx_reservations_user_id ON reservations(user_id);
CREATE INDEX idx_reservations_status ON reservations(status);
CREATE INDEX idx_reservations_start_time ON reservations(start_time);
CREATE INDEX idx_reservations_end_time ON reservations(end_time);

CREATE INDEX idx_usage_records_reservation_id ON usage_records(reservation_id);
CREATE INDEX idx_usage_records_instrument_id ON usage_records(instrument_id);
CREATE INDEX idx_usage_records_user_id ON usage_records(user_id);

CREATE INDEX idx_training_records_user_id ON training_records(user_id);
CREATE INDEX idx_training_records_instrument_id ON training_records(instrument_id);
CREATE INDEX idx_training_records_trainer_id ON training_records(trainer_id);
CREATE INDEX idx_training_records_status ON training_records(status);

CREATE INDEX idx_transactions_user_id ON transactions(user_id);
CREATE INDEX idx_transactions_research_group_id ON transactions(research_group_id);
CREATE INDEX idx_transactions_type ON transactions(type);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_transaction_date ON transactions(transaction_date);

CREATE INDEX idx_bills_research_group_id ON bills(research_group_id);
CREATE INDEX idx_bills_status ON bills(status);
CREATE INDEX idx_bills_bill_date ON bills(bill_date);

CREATE INDEX idx_bill_items_bill_id ON bill_items(bill_id);
CREATE INDEX idx_bill_items_reservation_id ON bill_items(reservation_id);

CREATE INDEX idx_deposit_records_research_group_id ON deposit_records(research_group_id);
CREATE INDEX idx_deposit_records_operator_id ON deposit_records(operator_id);
CREATE INDEX idx_deposit_records_status ON deposit_records(status);

-- 添加外键约束
ALTER TABLE users ADD CONSTRAINT fk_users_research_group
    FOREIGN KEY (research_group_id) REFERENCES research_groups(id) ON DELETE SET NULL;

ALTER TABLE research_groups ADD CONSTRAINT fk_research_groups_leader
    FOREIGN KEY (leader_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE instruments ADD CONSTRAINT fk_instruments_responsible_user
    FOREIGN KEY (responsible_user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE working_hours ADD CONSTRAINT fk_working_hours_instrument
    FOREIGN KEY (instrument_id) REFERENCES instruments(id) ON DELETE CASCADE;

ALTER TABLE billing_configs ADD CONSTRAINT fk_billing_configs_instrument
    FOREIGN KEY (instrument_id) REFERENCES instruments(id) ON DELETE CASCADE;

ALTER TABLE user_tags ADD CONSTRAINT fk_user_tags_instrument
    FOREIGN KEY (instrument_id) REFERENCES instruments(id) ON DELETE CASCADE;

ALTER TABLE reservations ADD CONSTRAINT fk_reservations_instrument
    FOREIGN KEY (instrument_id) REFERENCES instruments(id) ON DELETE CASCADE;
ALTER TABLE reservations ADD CONSTRAINT fk_reservations_user
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE reservations ADD CONSTRAINT fk_reservations_approved_by
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL;
ALTER TABLE reservations ADD CONSTRAINT fk_reservations_rejected_by
    FOREIGN KEY (rejected_by) REFERENCES users(id) ON DELETE SET NULL;

ALTER TABLE usage_records ADD CONSTRAINT fk_usage_records_reservation
    FOREIGN KEY (reservation_id) REFERENCES reservations(id) ON DELETE CASCADE;
ALTER TABLE usage_records ADD CONSTRAINT fk_usage_records_instrument
    FOREIGN KEY (instrument_id) REFERENCES instruments(id) ON DELETE CASCADE;
ALTER TABLE usage_records ADD CONSTRAINT fk_usage_records_user
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE training_records ADD CONSTRAINT fk_training_records_user
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE training_records ADD CONSTRAINT fk_training_records_instrument
    FOREIGN KEY (instrument_id) REFERENCES instruments(id) ON DELETE CASCADE;
ALTER TABLE training_records ADD CONSTRAINT fk_training_records_trainer
    FOREIGN KEY (trainer_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE transactions ADD CONSTRAINT fk_transactions_user
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE transactions ADD CONSTRAINT fk_transactions_research_group
    FOREIGN KEY (research_group_id) REFERENCES research_groups(id) ON DELETE CASCADE;
ALTER TABLE transactions ADD CONSTRAINT fk_transactions_reservation
    FOREIGN KEY (related_reservation_id) REFERENCES reservations(id) ON DELETE SET NULL;
ALTER TABLE transactions ADD CONSTRAINT fk_transactions_instrument
    FOREIGN KEY (related_instrument_id) REFERENCES instruments(id) ON DELETE SET NULL;

ALTER TABLE bills ADD CONSTRAINT fk_bills_research_group
    FOREIGN KEY (research_group_id) REFERENCES research_groups(id) ON DELETE CASCADE;

ALTER TABLE bill_items ADD CONSTRAINT fk_bill_items_bill
    FOREIGN KEY (bill_id) REFERENCES bills(id) ON DELETE CASCADE;
ALTER TABLE bill_items ADD CONSTRAINT fk_bill_items_reservation
    FOREIGN KEY (reservation_id) REFERENCES reservations(id) ON DELETE CASCADE;

ALTER TABLE deposit_records ADD CONSTRAINT fk_deposit_records_research_group
    FOREIGN KEY (research_group_id) REFERENCES research_groups(id) ON DELETE CASCADE;
ALTER TABLE deposit_records ADD CONSTRAINT fk_deposit_records_operator
    FOREIGN KEY (operator_id) REFERENCES users(id) ON DELETE CASCADE;

-- 创建触发器函数来自动更新updated_at字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表添加updated_at触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_research_groups_updated_at BEFORE UPDATE ON research_groups
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_instruments_updated_at BEFORE UPDATE ON instruments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reservations_updated_at BEFORE UPDATE ON reservations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_usage_records_updated_at BEFORE UPDATE ON usage_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_training_records_updated_at BEFORE UPDATE ON training_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_transactions_updated_at BEFORE UPDATE ON transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bills_updated_at BEFORE UPDATE ON bills
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_deposit_records_updated_at BEFORE UPDATE ON deposit_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();












