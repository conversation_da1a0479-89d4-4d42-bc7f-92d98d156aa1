package jwt

import (
	"time"

	"instrument-management/internal/config"
	"instrument-management/internal/models"

	"github.com/golang-jwt/jwt/v5"
)

// Claims JWT声明结构
type Claims struct {
	UserID string `json:"user_id"`
	Username string `json:"username"`
	Role    string `json:"role"`
	jwt.RegisteredClaims
}

// GenerateToken 生成JWT令牌
func GenerateToken(user *models.User) (string, error) {
	// 获取JWT配置
	secret := config.GetString("jwt.secret")
	expireHours := config.GetInt("jwt.expire_hours")

	// 设置过期时间
	expirationTime := time.Now().Add(time.Duration(expireHours) * time.Hour)

	// 创建声明
	claims := &Claims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     string(user.Role),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "instrument-management",
			Subject:   user.ID,
		},
	}

	// 创建令牌
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名令牌
	tokenString, err := token.SignedString([]byte(secret))
	if err != nil {
		return "", err
	}

	return tokenString, nil
}

// GenerateRefreshToken 生成刷新令牌
func GenerateRefreshToken(user *models.User) (string, error) {
	// 获取JWT配置
	secret := config.GetString("jwt.secret")
	refreshExpireHours := config.GetInt("jwt.refresh_expire_hours")

	// 设置过期时间（更长的过期时间）
	expirationTime := time.Now().Add(time.Duration(refreshExpireHours) * time.Hour)

	// 创建声明
	claims := &Claims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     string(user.Role),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "instrument-management-refresh",
			Subject:   user.ID,
		},
	}

	// 创建令牌
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名令牌
	tokenString, err := token.SignedString([]byte(secret))
	if err != nil {
		return "", err
	}

	return tokenString, nil
}

// ValidateToken 验证JWT令牌
func ValidateToken(tokenString string) (*Claims, error) {
	// 获取JWT密钥
	secret := config.GetString("jwt.secret")

	// 解析令牌
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(secret), nil
	})

	if err != nil {
		return nil, err
	}

	// 验证令牌
	if !token.Valid {
		return nil, jwt.ErrSignatureInvalid
	}

	// 获取声明
	claims, ok := token.Claims.(*Claims)
	if !ok {
		return nil, jwt.ErrTokenInvalidClaims
	}

	return claims, nil
}

// RefreshToken 刷新令牌
func RefreshToken(refreshTokenString string) (string, error) {
	// 验证刷新令牌
	claims, err := ValidateToken(refreshTokenString)
	if err != nil {
		return "", err
	}

	// 检查是否为刷新令牌
	if claims.Issuer != "instrument-management-refresh" {
		return "", jwt.ErrTokenInvalidIssuer
	}

	// 生成新的访问令牌
	// 注意：这里需要从数据库重新获取用户信息以确保数据是最新的
	newToken, err := GenerateToken(&models.User{
		ID:       claims.UserID,
		Username: claims.Username,
		Role:     models.UserRole(claims.Role),
	})

	if err != nil {
		return "", err
	}

	return newToken, nil
}

// ExtractUserID 从令牌中提取用户ID
func ExtractUserID(tokenString string) (string, error) {
	claims, err := ValidateToken(tokenString)
	if err != nil {
		return "", err
	}
	return claims.UserID, nil
}

// ExtractUsername 从令牌中提取用户名
func ExtractUsername(tokenString string) (string, error) {
	claims, err := ValidateToken(tokenString)
	if err != nil {
		return "", err
	}
	return claims.Username, nil
}

// ExtractRole 从令牌中提取用户角色
func ExtractRole(tokenString string) (string, error) {
	claims, err := ValidateToken(tokenString)
	if err != nil {
		return "", err
	}
	return claims.Role, nil
}












