# 西北工业大学大型仪器共享管理系统

## 🎯 项目概述

本系统是专为西北工业大学设计的大型仪器共享管理平台，旨在解决学校大型仪器设备使用效率低下、管理复杂的问题。通过现代化的技术架构，提供便捷的仪器预约、智能化计费、数据驱动决策等功能。

## ✨ 核心功能

### 🔐 用户管理系统
- **统一身份认证**：集成学校SSO系统
- **角色权限管理**：学生、教师、仪器负责人、管理员
- **课题组管理**：支持多课题组财务管理
- **用户激活流程**：学生需导师激活，教师直接激活

### 🧪 仪器管理
- **仪器状态监控**：实时显示仪器在线状态
- **预约管理**：自主预约和送样预约两种模式
- **计费配置**：灵活的计费策略（按时/按样品）
- **培训管理**：仪器培训记录和授权管理

### 💰 智能计费系统
- **自动计费**：基于使用时长自动计算费用
- **用户标签折扣**：支持院内/校内用户不同折扣
- **账单管理**：自动生成月度账单
- **财务统计**：收入分析和使用统计

### 📊 数据分析
- **使用统计**：仪器使用率分析
- **财务报表**：收入趋势和成本分析
- **用户行为**：预约频率和使用偏好
- **决策支持**：数据驱动的资源配置

## 🏗️ 技术架构

### 后端技术栈
- **语言**：Go 1.21
- **框架**：Gin (HTTP框架)
- **数据库**：PostgreSQL 15
- **缓存**：Redis 7
- **认证**：JWT
- **ORM**：GORM

### 前端技术栈
- **框架**：Flutter 3.x
- **平台**：Web, Android, iOS
- **状态管理**：Provider
- **网络请求**：Dio
- **UI组件**：Material Design

### 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter App   │    │   Gin Server    │    │  PostgreSQL DB  │
│   (Web/Android) │◄──►│   (Go Backend)  │◄──►│   (Database)    │
│                 │    │                 │    │                 │
│ • 预约界面      │    │ • REST API      │    │ • 用户表        │
│ • 仪器列表      │    │ • 认证中间件    │    │ • 仪器表        │
│ • 财务信息      │    │ • 业务逻辑      │    │ • 预约表        │
│ • 个人中心      │    │ • 数据验证      │    │ • 计费表        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 环境要求
- Go 1.21+
- Flutter 3.x
- PostgreSQL 15+
- Redis 7+ (可选)
- Docker & Docker Compose

### 1. 克隆项目
```bash
git clone https://github.com/your-repo/instrument-management.git
cd instrument-management
```

### 2. 后端设置

#### 使用Docker Compose（推荐）
```bash
cd backend
docker-compose up -d
```

#### 手动设置
```bash
# 安装依赖
cd backend
make deps

# 配置数据库
cp config.yaml.example config.yaml
# 编辑config.yaml配置数据库连接

# 执行数据库迁移
make migrate

# 运行后端服务
make run
```

### 3. 前端设置
```bash
cd instrument_management

# 安装Flutter依赖
flutter pub get

# 运行Web版本
flutter run -d web

# 运行Android版本
flutter run -d android

# 运行iOS版本（仅macOS）
flutter run -d ios
```

### 4. 访问应用
- **Web应用**：http://localhost:8080
- **后端API**：http://localhost:8080/api/v1
- **数据库管理**：http://localhost:5050 (PgAdmin)

## 📋 默认账号

系统初始化后提供以下默认账号：

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | admin123 | 管理员 | 系统管理员 |
| teacher1 | admin123 | 教师 | 张教授 |
| technician1 | admin123 | 仪器负责人 | 王技术员 |

## 🔧 开发指南

### 后端开发
```bash
# 代码格式化
make fmt

# 代码检查
make vet

# 运行测试
make test

# 构建
make build
```

### 前端开发
```bash
# 代码格式化
flutter format lib/

# 运行分析器
flutter analyze

# 运行测试
flutter test

# 构建Web版本
flutter build web
```

### 数据库迁移
```bash
# 创建新迁移
make migration

# 执行迁移
make migrate
```

## 📁 项目结构

```
instrument-management/
├── backend/                    # Go后端
│   ├── cmd/server/            # 应用程序入口
│   ├── internal/
│   │   ├── config/            # 配置管理
│   │   ├── database/          # 数据库连接
│   │   ├── handlers/          # HTTP处理器
│   │   ├── middleware/        # 中间件
│   │   ├── models/            # 数据模型
│   │   └── services/          # 业务逻辑
│   ├── migrations/            # 数据库迁移
│   ├── scripts/               # 工具脚本
│   ├── config.yaml            # 配置文件
│   ├── go.mod                 # Go模块
│   └── Dockerfile             # Docker构建
├── instrument_management/      # Flutter前端
│   ├── lib/
│   │   ├── models/            # 数据模型
│   │   ├── services/          # API服务
│   │   ├── screens/           # 页面组件
│   │   └── widgets/           # UI组件
│   ├── pubspec.yaml           # Flutter配置
│   └── web/                   # Web平台配置
├── docs/                      # 项目文档
└── README.md                  # 项目说明
```

## 🔒 安全说明

- **JWT密钥**：生产环境请修改`config.yaml`中的`jwt.secret`
- **数据库密码**：生产环境请使用强密码
- **HTTPS**：生产环境建议启用HTTPS
- **防火墙**：仅开放必要端口（8080, 5432）
- **备份**：定期备份数据库和配置文件

## 📊 API文档

启动服务后，可访问以下地址查看API文档：
- Swagger UI: http://localhost:8080/swagger/index.html
- ReDoc: http://localhost:8080/docs

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目维护者：西北工业大学信息技术中心
- 技术支持：<EMAIL>
- 项目主页：https://github.com/nwpu/instrument-management

---

**🎓 西北工业大学 - 让科技服务教学，让仪器创造价值**












