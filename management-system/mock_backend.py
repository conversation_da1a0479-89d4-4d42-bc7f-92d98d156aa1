#!/usr/bin/env python3
"""
西北工业大学仪器共享管理系统 - 模拟后端服务
用于演示Flutter前端与后端的集成
"""

import json
import uuid
from datetime import datetime, timedelta
from flask import Flask, request, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 模拟数据存储
users_db = {}
instruments_db = {}
reservations_db = {}

# 初始化模拟数据
def init_mock_data():
    # 创建默认用户
    users_db['admin'] = {
        'id': str(uuid.uuid4()),
        'username': 'admin',
        'email': '<EMAIL>',
        'full_name': '系统管理员',
        'role': 'admin',
        'status': 'active'
    }

    users_db['teacher1'] = {
        'id': str(uuid.uuid4()),
        'username': 'teacher1',
        'email': '<EMAIL>',
        'full_name': '张教授',
        'role': 'teacher',
        'status': 'active'
    }

    # 创建仪器数据
    instruments_db['1'] = {
        'id': '1',
        'name': '扫描电子显微镜',
        'description': '高分辨率扫描电子显微镜，用于材料微观结构分析',
        'model': 'SEM-1000',
        'manufacturer': 'NPU Instruments',
        'location': '理学院B101',
        'status': 'online',
        'control_type': 'computer',
        'requires_training': True,
        'accepts_sample_submission': False,
        'requires_reservation': True,
        'responsible_user_id': users_db['teacher1']['id'],
        'created_at': datetime.now().isoformat(),
        'updated_at': datetime.now().isoformat()
    }

    instruments_db['2'] = {
        'id': '2',
        'name': 'X射线衍射仪',
        'description': '用于晶体结构分析的X射线衍射仪',
        'model': 'XRD-2000',
        'manufacturer': 'NPU Instruments',
        'location': '材料学院A203',
        'status': 'in_use',
        'control_type': 'automated',
        'requires_training': True,
        'accepts_sample_submission': True,
        'requires_reservation': True,
        'responsible_user_id': users_db['teacher1']['id'],
        'created_at': datetime.now().isoformat(),
        'updated_at': datetime.now().isoformat()
    }

# API路由
@app.route('/api/v1/health', methods=['GET'])
def health_check():
    return jsonify({
        'success': True,
        'message': 'Mock backend is running',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/v1/auth/sso/login', methods=['POST'])
def sso_login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')

    # 简单认证逻辑
    if username in users_db and password == 'admin123':
        user = users_db[username]
        return jsonify({
            'success': True,
            'message': '登录成功',
            'data': {
                'token': f'mock_token_{username}',
                'refresh_token': f'mock_refresh_{username}',
                'user': user
            }
        })

    return jsonify({
        'success': False,
        'message': '用户名或密码错误'
    }), 401

@app.route('/api/v1/instruments', methods=['GET'])
def get_instruments():
    instruments = list(instruments_db.values())
    return jsonify({
        'success': True,
        'message': '获取成功',
        'data': {
            'instruments': instruments,
            'pagination': {
                'page': 1,
                'page_size': len(instruments),
                'total': len(instruments)
            }
        }
    })

@app.route('/api/v1/instruments/<instrument_id>', methods=['GET'])
def get_instrument(instrument_id):
    if instrument_id in instruments_db:
        return jsonify({
            'success': True,
            'message': '获取成功',
            'data': instruments_db[instrument_id]
        })

    return jsonify({
        'success': False,
        'message': '仪器不存在'
    }), 404

@app.route('/api/v1/reservations', methods=['GET'])
def get_reservations():
    reservations = list(reservations_db.values())
    return jsonify({
        'success': True,
        'message': '获取成功',
        'data': {
            'reservations': reservations,
            'pagination': {
                'page': 1,
                'page_size': len(reservations),
                'total': len(reservations)
            }
        }
    })

@app.route('/api/v1/reservations/self', methods=['POST'])
def create_self_reservation():
    data = request.get_json()

    # 检查用户认证（简化处理）
    # 实际项目中应该验证JWT token

    reservation = {
        'id': str(uuid.uuid4()),
        'instrument_id': data.get('instrument_id'),
        'user_id': data.get('user_id', 'mock_user_id'),
        'type': 'self_reservation',
        'status': 'pending',
        'start_time': data.get('start_time'),
        'end_time': data.get('end_time'),
        'notes': data.get('notes'),
        'created_at': datetime.now().isoformat(),
        'updated_at': datetime.now().isoformat()
    }

    reservations_db[reservation['id']] = reservation

    return jsonify({
        'success': True,
        'message': '预约创建成功',
        'data': reservation
    })

@app.route('/api/v1/billing/calculate/<reservation_id>', methods=['GET'])
def calculate_charge(reservation_id):
    # 模拟费用计算
    return jsonify({
        'success': True,
        'message': '计算成功',
        'data': {
            'reservation_id': reservation_id,
            'base_amount': 150.00,
            'discount': 0.1,
            'final_amount': 135.00,
            'billing_type': 'per_hour',
            'duration': 2.0
        }
    })

@app.route('/api/v1/billing/stats', methods=['GET'])
def get_billing_stats():
    return jsonify({
        'success': True,
        'message': '获取成功',
        'data': {
            'total_revenue': 15420.50,
            'monthly_revenue': 2340.75,
            'pending_payments': 1250.00,
            'total_transactions': 89,
            'revenue_by_instrument': {
                '扫描电子显微镜': 8920.50,
                'X射线衍射仪': 6500.00
            },
            'revenue_by_user_type': {
                '院内用户': 11250.25,
                '校内用户': 4170.25
            },
            'monthly_trend': [
                {'month': '2024-01', 'revenue': 2100.00, 'transaction_count': 12},
                {'month': '2024-02', 'revenue': 2340.75, 'transaction_count': 15}
            ]
        }
    })

@app.route('/api/v1/users/profile', methods=['GET'])
def get_user_profile():
    # 简化处理，返回管理员信息
    return jsonify({
        'success': True,
        'message': '获取成功',
        'data': users_db.get('admin', {})
    })

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'message': 'API端点不存在'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'message': '服务器内部错误'
    }), 500

if __name__ == '__main__':
    init_mock_data()
    print("🚀 西北工业大学仪器共享管理系统 - 模拟后端服务")
    print("📡 服务地址: http://localhost:8080")
    print("🔗 API文档: http://localhost:8080/api/v1/health")
    print("🎯 支持的端点:")
    print("  • POST /api/v1/auth/sso/login - 用户登录")
    print("  • GET  /api/v1/instruments - 获取仪器列表")
    print("  • GET  /api/v1/reservations - 获取预约列表")
    print("  • POST /api/v1/reservations/self - 创建自主预约")
    print("  • GET  /api/v1/billing/stats - 获取财务统计")
    print("  • GET  /api/v1/health - 健康检查")
    print("\n按 Ctrl+C 停止服务\n")

    app.run(host='localhost', port=8080, debug=True)












