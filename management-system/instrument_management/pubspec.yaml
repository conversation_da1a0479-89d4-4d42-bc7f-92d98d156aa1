name: instrument_management
description: 西北工业大学大型仪器共享管理系统
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2

  # 网络请求
  http: ^1.1.0
  dio: ^5.3.2

  # 状态管理
  provider: ^6.0.5
  flutter_riverpod: ^2.4.9

  # UI组件
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  flutter_staggered_grid_view: ^0.7.0

  # 日期时间选择器
  
  flutter_datetime_picker_plus: ^2.1.0
  table_calendar: ^3.0.9

  # 表单验证
  form_field_validator: ^1.1.0

  # 本地存储
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0

  # 权限管理
  permission_handler: ^11.3.0

  # 图片选择
  image_picker: ^1.0.4

  # 文件处理
  file_picker: ^6.1.1
  open_filex: ^4.3.4

  # 离线支持
  connectivity_plus: ^5.0.2

  # 国际化
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2

  # 工具库
  uuid: ^4.2.1
  path_provider: ^2.1.1
  url_launcher: ^6.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true

  # 资源文件
  assets:
    - assets/images/
    - assets/icons/

  # 字体
  fonts:
    - family: PingFang
      fonts:
        - asset: assets/fonts/PingFangSC-Regular.ttf
        - asset: assets/fonts/PingFangSC-Medium.ttf
          weight: 500
        - asset: assets/fonts/PingFangSC-Semibold.ttf
          weight: 600
