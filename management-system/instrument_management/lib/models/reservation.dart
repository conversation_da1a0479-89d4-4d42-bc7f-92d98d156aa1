/// 预约基础类
abstract class Reservation {
  final String id;
  final String instrumentId;
  final String userId;
  final ReservationStatus status;
  final DateTime startTime;
  final DateTime endTime;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Reservation({
    required this.id,
    required this.instrumentId,
    required this.userId,
    required this.status,
    required this.startTime,
    required this.endTime,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toJson();
}

/// 自主预约模型
class SelfReservation extends Reservation {
  final String? trainingRecordId; // 培训记录ID
  final List<String> formData; // 预约表单数据

  const SelfReservation({
    required super.id,
    required super.instrumentId,
    required super.userId,
    required super.status,
    required super.startTime,
    required super.endTime,
    super.notes,
    super.createdAt,
    super.updatedAt,
    this.trainingRecordId,
    this.formData = const [],
  });

  factory SelfReservation.fromJson(Map<String, dynamic> json) {
    return SelfReservation(
      id: json['id'] as String,
      instrumentId: json['instrument_id'] as String,
      userId: json['user_id'] as String,
      status: ReservationStatus.values.firstWhere(
        (status) => status.toString().split('.').last == json['status'],
        orElse: () => ReservationStatus.pending,
      ),
      startTime: DateTime.parse(json['start_time'] as String),
      endTime: DateTime.parse(json['end_time'] as String),
      notes: json['notes'] as String?,
      trainingRecordId: json['training_record_id'] as String?,
      formData: List<String>.from(json['form_data'] as List<dynamic>? ?? []),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'instrument_id': instrumentId,
      'user_id': userId,
      'type': 'self_reservation',
      'status': status.toString().split('.').last,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime.toIso8601String(),
      'notes': notes,
      'training_record_id': trainingRecordId,
      'form_data': formData,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

/// 送样预约模型
class SampleSubmission extends Reservation {
  final String sampleName;
  final String? sampleDescription;
  final int sampleCount;
  final List<String> attachments; // 附件文件URL列表
  final String? testReportUrl; // 测试报告URL
  final DateTime? completedAt;
  final SampleStatus sampleStatus;

  const SampleSubmission({
    required super.id,
    required super.instrumentId,
    required super.userId,
    required super.status,
    required super.startTime,
    required super.endTime,
    super.notes,
    super.createdAt,
    super.updatedAt,
    required this.sampleName,
    this.sampleDescription,
    required this.sampleCount,
    this.attachments = const [],
    this.testReportUrl,
    this.completedAt,
    required this.sampleStatus,
  });

  factory SampleSubmission.fromJson(Map<String, dynamic> json) {
    return SampleSubmission(
      id: json['id'] as String,
      instrumentId: json['instrument_id'] as String,
      userId: json['user_id'] as String,
      status: ReservationStatus.values.firstWhere(
        (status) => status.toString().split('.').last == json['status'],
        orElse: () => ReservationStatus.pending,
      ),
      startTime: DateTime.parse(json['start_time'] as String),
      endTime: DateTime.parse(json['end_time'] as String),
      notes: json['notes'] as String?,
      sampleName: json['sample_name'] as String,
      sampleDescription: json['sample_description'] as String?,
      sampleCount: json['sample_count'] as int,
      attachments:
          List<String>.from(json['attachments'] as List<dynamic>? ?? []),
      testReportUrl: json['test_report_url'] as String?,
      completedAt: json['completed_at'] != null
          ? DateTime.parse(json['completed_at'] as String)
          : null,
      sampleStatus: SampleStatus.values.firstWhere(
        (status) => status.toString().split('.').last == json['sample_status'],
        orElse: () => SampleStatus.pending,
      ),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'instrument_id': instrumentId,
      'user_id': userId,
      'type': 'sample_submission',
      'status': status.toString().split('.').last,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime.toIso8601String(),
      'notes': notes,
      'sample_name': sampleName,
      'sample_description': sampleDescription,
      'sample_count': sampleCount,
      'attachments': attachments,
      'test_report_url': testReportUrl,
      'completed_at': completedAt?.toIso8601String(),
      'sample_status': sampleStatus.toString().split('.').last,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

/// 预约状态枚举
enum ReservationStatus {
  pending, // 待审批
  approved, // 已批准
  rejected, // 已拒绝
  cancelled, // 已取消
  completed, // 已完成
  inProgress, // 进行中
}

/// 样品状态枚举
enum SampleStatus {
  pending, // 待接收
  received, // 已接收
  testing, // 测试中
  completed, // 已完成
  failed, // 测试失败
}

/// 使用记录模型
class UsageRecord {
  final String id;
  final String reservationId;
  final String instrumentId;
  final String userId;
  final DateTime actualStartTime;
  final DateTime? actualEndTime;
  final double actualDuration; // 实际使用时长（小时）
  final double chargeAmount; // 收费金额
  final BillingType billingType;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UsageRecord({
    required this.id,
    required this.reservationId,
    required this.instrumentId,
    required this.userId,
    required this.actualStartTime,
    this.actualEndTime,
    required this.actualDuration,
    required this.chargeAmount,
    required this.billingType,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UsageRecord.fromJson(Map<String, dynamic> json) {
    return UsageRecord(
      id: json['id'] as String,
      reservationId: json['reservation_id'] as String,
      instrumentId: json['instrument_id'] as String,
      userId: json['user_id'] as String,
      actualStartTime: DateTime.parse(json['actual_start_time'] as String),
      actualEndTime: json['actual_end_time'] != null
          ? DateTime.parse(json['actual_end_time'] as String)
          : null,
      actualDuration: (json['actual_duration'] as num).toDouble(),
      chargeAmount: (json['charge_amount'] as num).toDouble(),
      billingType: BillingType.values.firstWhere(
        (type) => type.toString().split('.').last == json['billing_type'],
        orElse: () => BillingType.perHour,
      ),
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'reservation_id': reservationId,
      'instrument_id': instrumentId,
      'user_id': userId,
      'actual_start_time': actualStartTime.toIso8601String(),
      'actual_end_time': actualEndTime?.toIso8601String(),
      'actual_duration': actualDuration,
      'charge_amount': chargeAmount,
      'billing_type': billingType.toString().split('.').last,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

/// 培训记录模型
class TrainingRecord {
  final String id;
  final String userId;
  final String instrumentId;
  final String trainerId;
  final DateTime trainingDate;
  final int duration; // 培训时长（分钟）
  final TrainingStatus status;
  final String? notes;
  final DateTime? expiryDate; // 培训过期日期
  final DateTime createdAt;
  final DateTime updatedAt;

  const TrainingRecord({
    required this.id,
    required this.userId,
    required this.instrumentId,
    required this.trainerId,
    required this.trainingDate,
    required this.duration,
    required this.status,
    this.notes,
    this.expiryDate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TrainingRecord.fromJson(Map<String, dynamic> json) {
    return TrainingRecord(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      instrumentId: json['instrument_id'] as String,
      trainerId: json['trainer_id'] as String,
      trainingDate: DateTime.parse(json['training_date'] as String),
      duration: json['duration'] as int,
      status: TrainingStatus.values.firstWhere(
        (status) => status.toString().split('.').last == json['status'],
        orElse: () => TrainingStatus.pending,
      ),
      notes: json['notes'] as String?,
      expiryDate: json['expiry_date'] != null
          ? DateTime.parse(json['expiry_date'] as String)
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'instrument_id': instrumentId,
      'trainer_id': trainerId,
      'training_date': trainingDate.toIso8601String(),
      'duration': duration,
      'status': status.toString().split('.').last,
      'notes': notes,
      'expiry_date': expiryDate?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

/// 培训状态枚举
enum TrainingStatus {
  pending, // 待培训
  scheduled, // 已安排
  completed, // 已完成
  expired, // 已过期
  failed, // 未通过
}
